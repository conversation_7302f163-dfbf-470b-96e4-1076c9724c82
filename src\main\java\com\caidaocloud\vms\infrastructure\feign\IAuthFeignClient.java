package com.caidaocloud.vms.infrastructure.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.vms.application.dto.RoleInitDto;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Auth服务Feign客户端
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@FeignClient(
        value = "caidaocloud-auth-service",
        fallback = AuthFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "authFeignClient"
)
public interface IAuthFeignClient {

    /**
     * 初始化租户角色
     *
     * @param roleInitDto 角色初始化信息
     * @return 初始化结果
     */
    @PostMapping("/api/auth/v1/tenant/init/role")
    Result<String> initTenantRole(@RequestBody RoleInitDto roleInitDto);
}

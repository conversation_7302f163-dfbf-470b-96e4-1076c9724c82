package com.caidaocloud.vms.application.vo;

import com.caidaocloud.vms.domain.base.enums.EmployeeManagementMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 规则集VO
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@Data
@ApiModel(description = "规则集详细信息")
public class RuleSetVO {
    
    @ApiModelProperty(value = "规则集ID")
    private String bid;
    
    @ApiModelProperty(value = "规则集名称")
    private String name;
    
    @ApiModelProperty(value = "规则集描述")
    private String description;
    
    @ApiModelProperty(value = "员工管理模式")
    private EmployeeManagementMode mode;
    
    @ApiModelProperty(value = "员工管理模式描述")
    private String modeDescription;
    
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Long updateTime;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}

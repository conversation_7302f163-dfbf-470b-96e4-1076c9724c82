package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.RuleSetDto;
import com.caidaocloud.vms.application.dto.RuleSetQueryDto;
import com.caidaocloud.vms.application.vo.RuleSetVO;
import com.caidaocloud.vms.domain.base.entity.RuleSet;
import com.caidaocloud.vms.domain.base.repository.RuleSetRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 规则集服务类
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@Service
public class RuleSetService {

    @Autowired
    private RuleSetRepository ruleSetRepository;

    /**
     * 创建规则集
     * 
     * @param ruleSetDto 规则集信息
     * @return 规则集ID
     */
    @PaasTransactional
    public String create(RuleSetDto ruleSetDto) {
        // 检查名称是否已存在
        if (ruleSetRepository.existsByName(ruleSetDto.getName(), null)) {
            throw new ServerException("规则集名称已存在: " + ruleSetDto.getName());
        }

        RuleSet ruleSet = new RuleSet(ruleSetDto.getName(), ruleSetDto.getMode());
        BeanUtils.copyProperties(ruleSetDto, ruleSet, "bid");
        
        ruleSetRepository.saveOrUpdate(ruleSet);
        return ruleSet.getBid();
    }

    /**
     * 更新规则集
     * 
     * @param ruleSetDto 规则集信息
     */
    @PaasTransactional
    public void update(RuleSetDto ruleSetDto) {
        if (ruleSetDto.getBid() == null) {
            throw new ServerException("规则集ID不能为空");
        }

        Optional<RuleSet> existingRuleSet = ruleSetRepository.getById(ruleSetDto.getBid());
        if (!existingRuleSet.isPresent()) {
            throw new ServerException("规则集不存在: " + ruleSetDto.getBid());
        }

        // 检查名称是否已存在（排除当前记录）
        if (ruleSetRepository.existsByName(ruleSetDto.getName(), ruleSetDto.getBid())) {
            throw new ServerException("规则集名称已存在: " + ruleSetDto.getName());
        }

        RuleSet ruleSet = existingRuleSet.get();
        BeanUtils.copyProperties(ruleSetDto, ruleSet, "bid", "createTime", "createBy");
        ruleSet.update();
        
        ruleSetRepository.saveOrUpdate(ruleSet);
    }

    /**
     * 根据ID获取规则集
     * 
     * @param id 规则集ID
     * @return 规则集信息
     */
    public RuleSetVO getById(String id) {
        Optional<RuleSet> ruleSetOpt = ruleSetRepository.getById(id);
        if (!ruleSetOpt.isPresent()) {
            throw new ServerException("规则集不存在: " + id);
        }

        RuleSet ruleSet = ruleSetOpt.get();
        RuleSetVO vo = ObjectConverter.convert(ruleSet, RuleSetVO.class);
        if (ruleSet.getMode() != null) {
            vo.setModeDescription(ruleSet.getMode().getDescription());
        }
        return vo;
    }

    /**
     * 分页查询规则集
     * 
     * @param queryDto 查询条件
     * @return 分页结果
     */
    public PageResult<RuleSetVO> findByPage(RuleSetQueryDto queryDto) {
        PageResult<RuleSet> pageResult = ruleSetRepository.findByPage(queryDto);
        
        List<RuleSetVO> voList = pageResult.getItems().stream()
                .map(ruleSet -> {
                    RuleSetVO vo = ObjectConverter.convert(ruleSet, RuleSetVO.class);
                    if (ruleSet.getMode() != null) {
                        vo.setModeDescription(ruleSet.getMode().getDescription());
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        
        return new PageResult<>(voList, pageResult.getTotal(), pageResult.getPageNum(), pageResult.getPageSize());
    }

    /**
     * 获取所有启用的规则集
     * 
     * @return 规则集列表
     */
    public List<RuleSetVO> findAllEnabled() {
        List<RuleSet> ruleSets = ruleSetRepository.findAllEnabled();
        
        return ruleSets.stream()
                .map(ruleSet -> {
                    RuleSetVO vo = ObjectConverter.convert(ruleSet, RuleSetVO.class);
                    if (ruleSet.getMode() != null) {
                        vo.setModeDescription(ruleSet.getMode().getDescription());
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 删除规则集
     * 
     * @param id 规则集ID
     */
    @PaasTransactional
    public void delete(String id) {
        Optional<RuleSet> ruleSetOpt = ruleSetRepository.getById(id);
        if (!ruleSetOpt.isPresent()) {
            throw new ServerException("规则集不存在: " + id);
        }

        ruleSetRepository.deleteById(id);
    }

    /**
     * 启用/禁用规则集
     * 
     * @param id 规则集ID
     * @param enabled 是否启用
     */
    @PaasTransactional
    public void updateStatus(String id, Boolean enabled) {
        Optional<RuleSet> ruleSetOpt = ruleSetRepository.getById(id);
        if (!ruleSetOpt.isPresent()) {
            throw new ServerException("规则集不存在: " + id);
        }

        RuleSet ruleSet = ruleSetOpt.get();
        ruleSet.setEnabled(enabled);
        ruleSet.update();
        
        ruleSetRepository.saveOrUpdate(ruleSet);
    }
}

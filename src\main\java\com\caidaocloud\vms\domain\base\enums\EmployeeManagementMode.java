package com.caidaocloud.vms.domain.base.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 * 员工管理模式枚举
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
public enum EmployeeManagementMode {
    /**
     * 外包员工管理
     */
    OUTSOURCED_ONLY(0, "外包员工管理"),
    
    /**
     * 自有员工+外包员工分开管理
     */
    SEPARATED_MANAGEMENT(1, "自有员工+外包员工分开管理");

    private final int code;
    private final String description;

    EmployeeManagementMode(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(this.code));
        return enumSimple;
    }

    public static EmployeeManagementMode fromCode(int code) {
        for (EmployeeManagementMode mode : EmployeeManagementMode.values()) {
            if (mode.getCode() == code) {
                return mode;
            }
        }
        throw new ServerException("Invalid EmployeeManagementMode code: " + code);
    }

    public static EmployeeManagementMode fromValue(EnumSimple value) {
        if (value == null || value.getValue() == null) {
            return null;
        }
        int code = Integer.parseInt(value.getValue());
        return fromCode(code);
    }
}

package com.caidaocloud.vms.application.service;

import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.application.vo.SupplierDetailVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Txt文本赋值功能测试
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@ExtendWith(MockitoExtension.class)
class TxtServiceTest {

    @Mock
    private ProjectService projectService;

    @Mock
    private SupplierService supplierService;

    @Mock
    private ProjectPositionService projectPositionService;

    @Test
    @DisplayName("测试项目详情txt文本赋值")
    void testLoadProjectTxt() {
        // Given
        String projectId = "test-project-123";
        ProjectVO mockVO = new ProjectVO();
        mockVO.setBid(projectId);
        mockVO.setProjectName("测试项目");
        mockVO.setCompanyTxt("测试公司");
        mockVO.setProjectManagerTxt("张三");
        mockVO.setProjectManagerOrgTxt("技术部");
        mockVO.setProjectManagerPostTxt("项目经理");

        when(projectService.loadProjectTxt(projectId)).thenReturn(mockVO);

        // When
        ProjectVO result = projectService.loadProjectTxt(projectId);

        // Then
        assertNotNull(result);
        assertEquals(projectId, result.getBid());
        assertEquals("测试公司", result.getCompanyTxt());
        assertEquals("张三", result.getProjectManagerTxt());
        assertEquals("技术部", result.getProjectManagerOrgTxt());
        assertEquals("项目经理", result.getProjectManagerPostTxt());

        verify(projectService, times(1)).loadProjectTxt(projectId);
    }

    @Test
    @DisplayName("测试供应商详情txt文本赋值")
    void testLoadSupplierTxt() {
        // Given
        String supplierId = "test-supplier-123";
        SupplierDetailVO mockVO = new SupplierDetailVO();
        mockVO.setBid(supplierId);
        mockVO.setSupplierName("测试供应商");

        when(supplierService.loadSupplierTxt(supplierId)).thenReturn(mockVO);

        // When
        SupplierDetailVO result = supplierService.loadSupplierTxt(supplierId);

        // Then
        assertNotNull(result);
        assertEquals(supplierId, result.getBid());
        assertEquals("测试供应商", result.getSupplierName());

        verify(supplierService, times(1)).loadSupplierTxt(supplierId);
    }

    @Test
    @DisplayName("测试岗位详情txt文本赋值")
    void testGetPositionDetailTxt() {
        // Given
        String positionId = "test-position-123";
        ProjectPositionVO mockVO = new ProjectPositionVO();
        mockVO.setBid(positionId);
        mockVO.setCompanyTxt("测试公司");
        mockVO.setOrganizeTxt("技术部");
        mockVO.setPositionTxt("Java开发工程师");

        when(projectPositionService.getPositionDetailTxt(positionId)).thenReturn(mockVO);

        // When
        ProjectPositionVO result = projectPositionService.getPositionDetailTxt(positionId);

        // Then
        assertNotNull(result);
        assertEquals(positionId, result.getBid());
        assertEquals("测试公司", result.getCompanyTxt());
        assertEquals("技术部", result.getOrganizeTxt());
        assertEquals("Java开发工程师", result.getPositionTxt());

        verify(projectPositionService, times(1)).getPositionDetailTxt(positionId);
    }
}

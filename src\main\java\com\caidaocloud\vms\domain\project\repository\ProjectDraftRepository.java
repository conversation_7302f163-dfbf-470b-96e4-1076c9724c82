package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;

import java.util.List;
import java.util.Optional;

/**
 * 项目草稿Repository接口
 * 
 * <AUTHOR>
 * @date 2025/10/10
 */
public interface ProjectDraftRepository {

    /**
     * 保存或更新项目草稿
     * @param projectDraft 项目草稿
     * @return 草稿ID
     */
    String saveOrUpdate(ProjectDraft projectDraft);

    /**
     * 根据ID获取项目草稿
     * @param draftId 草稿ID
     * @return 项目草稿
     */
    Optional<ProjectDraft> getById(String draftId);

    /**
     * 根据targetId获取项目草稿
     * @param targetId 目标实体ID
     * @return 项目草稿
     */
    Optional<ProjectDraft> getByTargetId(String targetId);

    /**
     * 根据项目ID和类型获取草稿列表
     * @param projectId 项目ID
     * @param historyType 历史类型
     * @return 草稿列表
     */
    List<ProjectDraft> getByProjectIdAndType(String projectId, HistoryType historyType);

    /**
     * 根据项目ID、类型和操作类型获取草稿列表
     * @param projectId 项目ID
     * @param historyType 历史类型
     * @param operationType 操作类型
     * @return 草稿列表
     */
    List<ProjectDraft> getByProjectIdAndTypeAndOperation(String projectId, HistoryType historyType, OperationType operationType);

    /**
     * 删除项目草稿
     * @param draftId 草稿ID
     */
    void delete(String draftId);

    /**
     * 根据targetId删除项目草稿
     * @param targetId 目标实体ID
     */
    void deleteByTargetId(String targetId);

    /**
     * 根据项目ID删除所有草稿
     * @param projectId 项目ID
     */
    void deleteByProjectId(String projectId);

	List<ProjectDraft> getByPositionId(String bid);

    List<ProjectDraft> getByProjectId(String bid);

    List<ProjectDraft> listByTargetId(List<String> positionIds);

    List<ProjectDraft> listByPositionIds(List<String> positionIds);

	List<ProjectDraft> listByIds(List<String> draftIds);

    void deleteByIds(List<String> draftIds);
}

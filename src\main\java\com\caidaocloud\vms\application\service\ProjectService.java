package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.application.dto.ProjectQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.application.vo.ProjectSimpleVO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.application.service.emp.CompanyService;
import com.caidaocloud.vms.application.service.emp.EmpService;
import com.caidaocloud.vms.application.vo.workflow.WorkflowProjectVO;
import com.caidaocloud.vms.domain.base.repository.CompanyRepository;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.factory.ProjectFactory;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.project.repository.ProjectContactRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectPositionRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import com.caidaocloud.vms.domain.project.repository.ProjectSupplierRepository;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/6/3
 */
@Service
@Slf4j
public class ProjectService {
	@Autowired
	private ProjectRepository projectRepository;

	@Autowired
	private ProjectHistoryFactory projectHistoryFactory;
	@Autowired
	private ProjectHistoryService projectHistoryService;

	@Autowired
	private ProjectSettingRepository projectSettingRepository;

	@Autowired
	private ProjectDraftRepository projectDraftRepository;

	@Autowired
	private WorkflowService workflowService;

	@Autowired
	private ProjectPositionRepository projectPositionRepository;

	@Autowired
	private ProjectSupplierRepository projectSupplierRepository;

	@Autowired
	private ProjectContactRepository projectContactRepository;

	@Autowired
	private CompanyRepository companyRepository;

	@Autowired
	private CompanyService companyService;

	@Autowired
	private EmpService empService;

	/**
	 * 传入项目编码（必填）、项目名称（必填）、开始日期（时间戳），结束日期，预算总额，计划人员数量，项目负责人，所属公司，备注
	 * 保存项目基本信息，并初始化项目设置
	 *
	 * @return
	 */
	// @PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.CREATE)
	public String save(ProjectDto projectDto) {
		// 创建项目基本信息
		Project project = ProjectFactory.create(projectDto, workflowService.checkWorkflowEnable());

		// 保存项目
		projectRepository.saveOrUpdate(project);
		projectSettingRepository.init(project.getProjectSetting(), project.getBid());
		return project.getBid();
	}

	/**
	 * 编辑项目信息
	 * 
	 * @param projectDto 项目信息
	 */
	// @PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.UPDATE)
	public void edit(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		project.checkUpdate();
		// TODO: 2025/10/14 已用预算、实际人员

		// 更新项目信息
		project.updateBasicInfo(projectDto);
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	/**
	 * 分页查询项目列表
	 * 
	 * @param queryDTO 查询条件
	 * @return 项目列表
	 */
	public PageResult<ProjectVO> projectPage(ProjectQueryDTO queryDTO) {
		// 执行分页查询
		PageResult<Project> projectPage = projectRepository.findByPage(queryDTO.getProjectName(), queryDTO);

		// 转换为VO列表
		List<ProjectVO> voList = projectPage.getItems().stream()
				.map(project -> {
					ProjectVO vo = ObjectConverter.convert(project, ProjectVO.class);
					// 设置状态枚举
					if (project.getStatus() != null) {
						vo.setStatus(ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue())));
					}
					return vo;
				})
				.collect(Collectors.toList());

		return new PageResult<>(voList, projectPage.getPageNo(), projectPage.getPageSize(), projectPage.getTotal());
	}

	/**
	 * 根据ID加载项目基础信息
	 * 
	 * @param projectId 项目ID
	 * @return 项目详细信息
	 */
	public ProjectVO loadProject(String projectId) {
		// 根据ID查询项目
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 转换为VO
		ProjectVO vo = ObjectConverter.convert(project, ProjectVO.class);

		// 设置状态枚举
		if (project.getStatus() != null) {
			vo.setStatus(ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue())));
		}

		return vo;
	}

	/**
	 * 删除项目
	 * 
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.DELETE)
	public void delete(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}

		// 删除项目
		projectRepository.delete(project);
	}

	/**
	 * 提交项目变更
	 * 根据draft生成change，保存historyDetail，发起工作流
	 * 无分布式事务，手动回滚
	 *
	 * @param projectId 项目ID
	 */
	public void commitProject(ProjectDto project) {
		SpringUtil.getBean(ProjectService.class).edit(project);
		doCommit(project.getBid());
	}

	private void doCommit(String projectId) {
		// 获取项目信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 获取项目相关的所有草稿
		Optional<ProjectDraft> optional = projectDraftRepository.getByTargetId(project.getBid());
		if (!optional.isPresent()) {
			log.info("No drafts found for project: " + projectId);
			return;
		}

		ProjectHistory projectHistory = null;
		try {
			ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(project,
					optional.get());
			projectHistory = new ProjectHistory(projectId, historyDetail);
			projectHistoryService.saveHistory(projectHistory);

			workflowService.startWorkflow(projectHistory);

		} catch (WorkflowStartException e) {
			projectHistoryService.rollbackHistory(projectHistory);
			throw new ServerException("Failed to start workflow for project: " + projectId, e);
		} catch (Exception e) {
			throw new ServerException("Failed to commit project: " + projectId, e);
		}
	}

	public List<ProjectSimpleVO> loadProjectSelectList() {

		// 批量查询项目信息
		List<Project> projects = projectRepository.loadList();
		if (CollectionUtils.isEmpty(projects)) {
			return new ArrayList<>();
		}
		List<String> projectIds = Sequences.sequence(projects).map(AbstractData::getBid).toList();
		List<ProjectSetting> projectSettings = projectSettingRepository.loadList(projectIds);

		// 转换为VO
		return projects.stream()
				.map(project -> {
					ProjectSimpleVO vo = ObjectConverter.convert(project, ProjectSimpleVO.class);
					Option<ProjectSetting> settingOption = Sequences.sequence(projectSettings)
							.find(s -> project.getBid().equals(s.getProjectId()));
					if (settingOption.isDefined()) {
						vo.setSetting(ObjectConverter.convert(settingOption.get(), ProjectSettingVO.class));
					}
					return vo;
				})
				.collect(Collectors.toList());
	}

	public void start(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		ProjectStatus status = ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue()));
		if (status != ProjectStatus.APPROVED) {
			throw new ServerException("项目不可开始");
		}

		// 更新项目信息
		project.start();
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	public void end(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		ProjectStatus status = ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue()));
		if (status != ProjectStatus.IN_PROGRESS) {
			throw new ServerException("项目不可关闭");
		}

		// 更新项目信息
		project.end();
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	public void refreshBudget(String projectId) {
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}
		List<ProjectPosition> positionList = projectPositionRepository.loadPositionList(projectId);
		BigDecimal totalBudget = new BigDecimal(0);
		if (CollectionUtils.isEmpty(positionList)) {
			return;
		}
		for (ProjectPosition position : positionList) {
			if (position.getStatus() != PositionStatus.UN_SUBMITTED && position.getTotalBudget() != null) {
				totalBudget = totalBudget.add(BigDecimal.valueOf(position.getTotalBudget()));
			}
		}
		project.setTotalBudget(totalBudget);
		project.update();
		projectRepository.saveOrUpdate(project);
	}

	public Project loadProjectAll(String projectId) {
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}
		project.setProjectSetting(projectSettingRepository.getByProjectId(projectId).get());
		project.setSupplierList(projectSupplierRepository.findByProjectId(projectId));
		project.setContactList(projectContactRepository.loadContactList(projectId));
		project.setPositionList(projectPositionRepository.loadPositionList(projectId));
		return project;
	}

	public WorkflowProjectVO loadProjectTxt(String projectId) {
		ProjectVO vo = loadProject(projectId);

		WorkflowProjectVO workflowProjectVO = ObjectConverter.convert(vo, WorkflowProjectVO.class);
		workflowProjectVO.setProjectManager(Optional.ofNullable(vo.getProjectManager()).map(EmpSimple::getName).orElse(null));
		// 为公司字段赋值txt文本
			if (vo.getCompany() != null) {
				List<CompanyInfoDto> companyList = companyService.loadCompanyList(Arrays.asList(vo.getCompany()));
				if (!companyList.isEmpty()) {
					workflowProjectVO.setCompany(companyList.get(0).getCompanyName());
				}
			}


		return workflowProjectVO;
	}
}

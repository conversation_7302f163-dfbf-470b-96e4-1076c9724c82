package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceQueryDto;
import com.caidaocloud.vms.domain.project.entity.PositionSalaryReference;
import com.caidaocloud.vms.domain.project.repository.PositionSalaryReferenceRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 岗位薪资参考Repository实现
 *
 * @date 2026/01/08
 */
@Repository
public class PositionSalaryReferenceRepositoryImpl implements PositionSalaryReferenceRepository {

    @Override
    public void saveOrUpdate(PositionSalaryReference entity) {
        if (entity.getBid() == null) {
            DataInsert.identifier(PositionSalaryReference.identifier).insert(entity);
        } else {
            DataUpdate.identifier(PositionSalaryReference.identifier).update(entity);
        }
    }

    @Override
    public PositionSalaryReference findByBid(String bid) {
        return DataQuery.identifier(PositionSalaryReference.identifier)
                .oneOrNull(bid, PositionSalaryReference.class);
    }

    @Override
    public void deleteByBid(String bid) {
        DataDelete.identifier(PositionSalaryReference.identifier).softDelete(bid);
    }

    @Override
    public PageResult<PositionSalaryReference> findByPage(PositionSalaryReferenceQueryDto queryDto) {
        DataFilter filter = buildFilter(queryDto);
        return DataQuery.identifier(PositionSalaryReference.identifier)
                .limit(queryDto.getPageSize(), queryDto.getPageNo())
                .filter(filter, PositionSalaryReference.class);
    }

    @Override
    public List<PositionSalaryReference> findAll(PositionSalaryReferenceQueryDto queryDto) {
        DataFilter filter = buildFilter(queryDto);
        PageResult<PositionSalaryReference> result = DataQuery.identifier(PositionSalaryReference.identifier)
                .limit(10000, 1)
                .filter(filter, PositionSalaryReference.class);
        return result.getItems();
    }

    @Override
    public void batchSave(List<PositionSalaryReference> entities) {
        for (PositionSalaryReference entity : entities) {
            saveOrUpdate(entity);
        }
    }

    @Override
    public void batchDelete(List<String> bids) {
        if (bids != null && !bids.isEmpty()) {
            DataDelete.identifier(PositionSalaryReference.identifier).batchDelete(DataFilter.in("bid", bids));
        }
    }

    private DataFilter buildFilter(PositionSalaryReferenceQueryDto queryDto) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());

        if (queryDto.getYear() != null) {
            filter = filter.andEq("year", String.valueOf(queryDto.getYear()));
        }

        if (queryDto.getCityCodes() != null && !queryDto.getCityCodes().isEmpty()) {
            filter = filter.andIn("cityCode", queryDto.getCityCodes());
        }

        if (queryDto.getIndustryCodes() != null && !queryDto.getIndustryCodes().isEmpty()) {
            filter = filter.andIn("industryCode", queryDto.getIndustryCodes());
        }

        if (StringUtils.isNotBlank(queryDto.getPositionName())) {
            filter = filter.andRegex("positionName", queryDto.getPositionName());
        }

        return filter;
    }
}

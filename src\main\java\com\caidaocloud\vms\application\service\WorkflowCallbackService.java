package com.caidaocloud.vms.application.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.project.entity.*;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.project.repository.*;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 工作流回调服务
 * 处理工作流审批结果的业务逻辑
 *
 * <AUTHOR> Zhou
 * @date 2025/10/17
 */
@Service
@Slf4j
public class WorkflowCallbackService {

    @Autowired
    private ProjectHistoryService projectHistoryService;

    @Autowired
    private ProjectHistoryRepository projectHistoryRepository;

    @Autowired
    private ProjectDraftRepository projectDraftRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPositionRepository projectPositionRepository;

    @Autowired
    private ProjectContactRepository projectContactRepository;

    @Autowired
    private ProjectSupplierRepository projectSupplierRepository;

    @Autowired
    private PositionSupplierRepository positionSupplierRepository;
    @Autowired
    private PositionRequirementRepository positionRequirementRepository;

    /**
     * 处理项目工作流审批通过
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void approveProjectWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目工作流审批通过，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

            Project project = projectRepository.getById(history.getProjectId());
            project.approve();
            projectRepository.saveOrUpdate(project);
            // TODO: 2025/10/24  是否修改所有岗位的审批状态

            // 清理草稿数据
            deleteHistoryDraft(history);

            log.info("项目工作流审批通过处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目工作流审批通过失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目工作流审批通过失败", e);
        }
    }

    private void deleteHistoryDraft(ProjectHistory history) {
        List<ProjectHistoryDetail> list = projectHistoryService.loadDetail(history.getBid());
        List<String> draftIds = Sequences.sequence(list).flatMap(ProjectHistoryDetail::getDraft).toList();
        // 获取项目相关的所有草稿
         projectDraftRepository.deleteByIds(draftIds);
    }

    /**
     * 处理项目工作流审批拒绝
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void rejectProjectWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目工作流审批拒绝，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            Project project = projectRepository.getById(history.getProjectId());
            project.reject();
            projectRepository.saveOrUpdate(project);

            log.info("项目工作流审批拒绝处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目工作流审批拒绝失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目工作流审批拒绝失败", e);
        }
    }

    /**
     * 处理项目岗位工作流审批通过
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void approveProjectPositionWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目岗位工作流审批通过，单据id：{}，岗位id: {}", history.getBid(), history.getPositionId());
            Optional<ProjectPosition> position = projectPositionRepository.getPosition(history.getPositionId());
            if (!position.isPresent()) {
                log.error("岗位审批回调失败，岗位不存在，单据id:{},岗位id:{}", history.getBid(), history.getPositionId());
                return;
            }
            position.get().approve();
            projectPositionRepository.saveOrUpdate(position.get());

            deleteHistoryDraft(history);
            log.info("项目岗位工作流审批通过处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目岗位工作流审批通过失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目岗位工作流审批通过失败", e);
        }
    }

    private List<ProjectDraft>  loadHistoryDraftList(ProjectHistory history) {
        List<ProjectHistoryDetail> list = projectHistoryService.loadDetail(history.getBid());
        List<String> draftIds = Sequences.sequence(list).flatMap(ProjectHistoryDetail::getDraft).toList();
        // 获取项目相关的所有草稿
        return projectDraftRepository.listByIds(draftIds);
    }

    /**
     * 处理项目岗位工作流审批拒绝
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void rejectProjectPositionWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目岗位工作流审批拒绝，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            Optional<ProjectPosition> position = projectPositionRepository.getPosition(history.getPositionId());
            if (!position.isPresent()) {
                log.error("岗位审批回调失败，岗位不存在，单据id:{},岗位id:{}", history.getBid(), history.getPositionId());
                return;
            }
            position.get().reject();
            projectPositionRepository.saveOrUpdate(position.get());
            // List<ProjectDraft> drafts = loadHistoryDraftList(history);
            // if (drafts.isEmpty()) {
            //     log.warn("未找到项目相关的草稿数据，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            //     return;
            // }
            //
            // // 处理拒绝逻辑
            // for (ProjectDraft draft : drafts) {
            //     processPositionDraftRejection(draft);
            // }

            // // 清理岗位草稿数据
            // for (ProjectDraft draft : drafts) {
            //     projectDraftRepository.delete(draft.getBid());
            // }

            log.info("项目岗位工作流审批拒绝处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目岗位工作流审批拒绝失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目岗位工作流审批拒绝失败", e);
        }
    }

    @PaasTransactional
    public void callback(String businessKey, String tenantId, WfCallbackTriggerOperationEnum callback) {
// 设置回调用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        // 回调默认用户id为 0
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            SpringUtil.getBean(WorkflowCallbackService.class).doCallback(businessKey, callback);
        } catch (Exception e) {
            throw new ServerException("callback occurs error", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    public void doCallback(String businessKey, WfCallbackTriggerOperationEnum callback) {
        String businessType = StringUtils.substringAfter(businessKey, "_");
        WorkflowConfig type = WorkflowConfig.fromCode(businessType);
        Optional<ProjectHistory> projectHistoryOptional = projectHistoryService.loadByBusinessKey(businessKey);
        PreCheck.preCheckArgument(!projectHistoryOptional.isPresent(), "apply not exist");

        ProjectHistory history = projectHistoryOptional.get();

        if (!String.valueOf(ApprovalStatus.PENDING.getCode()).equals(history.getApproveStatus().getValue())) {
            log.warn("Project workflow process is ended,businessKey={},status={}", businessKey, history.getApproveStatus());
            return;
        }
        switch (callback) {
        case APPROVED:
            // TODO: 2025/11/4 修改historyDetail状态
            history.setApproveStatus(ApprovalStatus.APPROVED.toEnumSimple());
            history.update();
            if (type == WorkflowConfig.PROJECT_MANAGEMENT) {
                approveProjectWorkflow(history);
            }
            else {
                approveProjectPositionWorkflow(history);
            }
            break;
        case REFUSED:
            history.setApproveStatus(ApprovalStatus.REJECTED.toEnumSimple());
            history.update();
            if (type == WorkflowConfig.PROJECT_MANAGEMENT) {
                rejectProjectWorkflow(history);
            }
            else {
                rejectProjectPositionWorkflow(history);
            }
            break;
        case ERROR:
        case TIMED_TASK:
        case REVOKE:
        default:
            // 回调类型不支持
            throw new ServerException("callback type not support");
        }
        projectHistoryRepository.saveOrUpdate(history);
    }
}

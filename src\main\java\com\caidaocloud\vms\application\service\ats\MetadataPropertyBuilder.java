package com.caidaocloud.vms.application.service.ats;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.RuleDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.googlecode.totallylazy.Pair;
import com.ruipin.metadata.common.component.ComponentTypeEnum;
import com.ruipin.metadata.domain.vo.EntityPropertyDefVo;

/**
 * MetadataPropertyDto构建器
 * 使用Builder模式优化property的convert逻辑
 * 
 * <AUTHOR>
 * @date 2026/1/12
 */
public class MetadataPropertyBuilder {
    
    private String propertyName;
    private String inputType;
    private PropertyDataType dataType;
    private String widgetType;
    private List<RuleDto> rules;
    private EntityPropertyDefVo sourceDefinition;
    
    private MetadataPropertyBuilder() {
        this.rules = new ArrayList<>();
    }
    
    /**
     * 创建Builder实例
     * 
     * @param propertyName 属性名称
     * @param inputType 输入类型
     * @return Builder实例
     */
    public static MetadataPropertyBuilder create(String propertyName, String inputType) {
        MetadataPropertyBuilder builder = new MetadataPropertyBuilder();
        builder.propertyName = propertyName;
        builder.inputType = inputType;
        return builder;
    }
    
    /**
     * 从EntityPropertyDefVo创建Builder实例
     * 
     * @param def EntityPropertyDefVo定义
     * @return Builder实例
     */
    public static MetadataPropertyBuilder fromEntityPropertyDef(EntityPropertyDefVo def) {
        MetadataPropertyBuilder builder = create(def.getPropertyName(), def.getInputType());
        builder.sourceDefinition = def;
        return builder;
    }
    
    /**
     * 转换并设置数据类型和控件类型
     * 
     * @return Builder实例
     */
    public MetadataPropertyBuilder convertTypes() {
        Pair<PropertyDataType, String> paasType = convert2PaasType(inputType);
        this.dataType = paasType.first();
        this.widgetType = paasType.second();
        return this;
    }
    
    /**
     * 添加规则
     * 
     * @param rule 规则
     * @return Builder实例
     */
    public MetadataPropertyBuilder addRule(RuleDto rule) {
        this.rules.add(rule);
        return this;
    }
    
    /**
     * 添加规则
     * 
     * @param type 规则类型
     * @param value 规则值
     * @return Builder实例
     */
    public MetadataPropertyBuilder addRule(String type, String value) {
        RuleDto rule = new RuleDto();
        rule.setType(type);
        rule.setValue(value);
        return addRule(rule);
    }
    
    /**
     * 根据控件类型自动添加默认规则
     * 
     * @return Builder实例
     */
    public MetadataPropertyBuilder applyDefaultRules() {
        if ("textArea".equals(widgetType)) {
            addRule("maxLength", "2000");
        }
        // 可以在这里添加更多默认规则
        return this;
    }
    
    /**
     * 构建MetadataPropertyDto对象
     * 
     * @return MetadataPropertyDto实例
     */
    public MetadataPropertyDto build() {
        MetadataPropertyDto dto = new MetadataPropertyDto();
        dto.setName(propertyName);
        dto.setDataType(dataType);
        dto.setWidgetType(widgetType);
        dto.setRules(new ArrayList<>(rules));
        
        // TODO: 2026/1/12 字段名ats没有
        // if (sourceDefinition != null && sourceDefinition.getPropertyEngName() != null) {
        //     dto.setProperty(sourceDefinition.getPropertyEngName());
        // }
        
        return dto;
    }
    
    /**
     * 转换ATS输入类型到PAAS类型
     * 
     * @param inputType ATS输入类型
     * @return PAAS数据类型和控件类型的配对
     */
    private Pair<PropertyDataType, String> convert2PaasType(String inputType) {
        try {
            ComponentTypeEnum typeEnum = ComponentTypeEnum.valueOf(inputType);
            PropertyDataType dataType = null;
            String widgetType = null;
            
            switch (typeEnum) {
                case input:
                    dataType = PropertyDataType.String;
                    widgetType = "text";
                    break;
                case txt_area:
                    dataType = PropertyDataType.String;
                    widgetType = "textArea";
                    break;
                case check:
                    dataType = PropertyDataType.Dict;
                    widgetType = "EnumSelect";
                    break;
                case select:
                    dataType = PropertyDataType.Enum;
                    widgetType = "enum";
                    break;
                case date_year_month:
                case date_year_month_day:
                    dataType = PropertyDataType.Timestamp;
                    widgetType = "date";
                    break;
                case input_number:
                    dataType = PropertyDataType.Number;
                    widgetType = "float";
                    break;
                default:
                    throw new ServerException("不支持的ats字段类型" + inputType);
            }
            return Pair.pair(dataType, widgetType);
        } catch (Exception e) {
            throw new ServerException("ats字段类型" + inputType + "不存在");
        }
    }
}

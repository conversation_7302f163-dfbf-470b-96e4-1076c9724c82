package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 岗位薪资参考查询DTO
 *
 * @date 2026/01/08
 */
@Data
@ApiModel("岗位薪资参考查询")
public class PositionSalaryReferenceQueryDto extends BasePage {

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("城市编码列表（多选）")
    private List<String> cityCodes;

    @ApiModelProperty("行业编码列表（多选）")
    private List<String> industryCodes;

    @ApiModelProperty("岗位名称（模糊匹配）")
    private String positionName;
}

package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 岗位薪资参考新增DTO
 *
 * @date 2026/01/08
 */
@Data
@ApiModel("岗位薪资参考新增")
public class PositionSalaryReferenceCreateDto {

    @NotNull(message = "年份不能为空")
    @ApiModelProperty(value = "年份", required = true)
    private Integer year;

    @NotBlank(message = "城市编码不能为空")
    @ApiModelProperty(value = "城市编码", required = true)
    private String cityCode;

    @NotBlank(message = "城市名称不能为空")
    @ApiModelProperty(value = "城市名称", required = true)
    private String cityName;

    @NotBlank(message = "行业编码不能为空")
    @ApiModelProperty(value = "行业编码", required = true)
    private String industryCode;

    @NotBlank(message = "行业名称不能为空")
    @ApiModelProperty(value = "行业名称", required = true)
    private String industryName;

    @NotBlank(message = "岗位名称不能为空")
    @ApiModelProperty(value = "岗位名称", required = true)
    private String positionName;

    @NotBlank(message = "经验要求不能为空")
    @ApiModelProperty(value = "经验要求（如：3-5年）", required = true)
    private String experienceName;

    @ApiModelProperty("25分位薪资（万元/年）")
    private String percentile25;

    @ApiModelProperty("50分位薪资（万元/年）")
    private String percentile50;

    @ApiModelProperty("75分位薪资（万元/年）")
    private String percentile75;

    @ApiModelProperty("90分位薪资（万元/年）")
    private String percentile90;
}

package com.caidaocloud.vms.application.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 岗位薪资参考VO
 *
 * @date 2026/01/08
 */
@Data
@ApiModel("岗位薪资参考")
public class PositionSalaryReferenceVO {

    @ApiModelProperty("业务ID")
    private String bid;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("城市编码")
    private String cityCode;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("行业编码")
    private String industryCode;

    @ApiModelProperty("行业名称")
    private String industryName;

    @ApiModelProperty("岗位名称")
    private String positionName;

    @ApiModelProperty("经验要求")
    private String experienceName;

    @ApiModelProperty("25分位薪资（万元/年）")
    private String percentile25;

    @ApiModelProperty("50分位薪资（万元/年）")
    private String percentile50;

    @ApiModelProperty("75分位薪资（万元/年）")
    private String percentile75;

    @ApiModelProperty("90分位薪资（万元/年）")
    private String percentile90;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("更新时间")
    private Long updateTime;
}

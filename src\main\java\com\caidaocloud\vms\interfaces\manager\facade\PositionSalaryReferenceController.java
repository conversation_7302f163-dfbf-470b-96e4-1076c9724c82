package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceCreateDto;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceQueryDto;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceUpdateDto;
import com.caidaocloud.vms.application.service.PositionSalaryReferenceOptionService;
import com.caidaocloud.vms.application.service.PositionSalaryReferenceService;
import com.caidaocloud.vms.application.vo.CascadeOptionVO;
import com.caidaocloud.vms.application.vo.ImportResultVO;
import com.caidaocloud.vms.application.vo.PositionSalaryReferenceVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api/vms/v1/manager/salary-reference")
@Api(tags = "岗位薪资参考")
public class PositionSalaryReferenceController {

    @Autowired
    private PositionSalaryReferenceOptionService optionService;

    @Autowired
    private PositionSalaryReferenceService salaryReferenceService;

    @GetMapping("/options/industry")
    @ApiOperation("获取行业级联选项")
    public Result<List<CascadeOptionVO>> getIndustryOptions() {
        return Result.ok(optionService.getIndustryOptions());
    }

    @GetMapping("/options/city")
    @ApiOperation("获取城市级联选项")
    public Result<List<CascadeOptionVO>> getCityOptions() {
        return Result.ok(optionService.getCityOptions());
    }


    @PostMapping("/create")
    @ApiOperation("新增岗位薪资参考")
    public Result create(@Valid @RequestBody PositionSalaryReferenceCreateDto dto) {
        salaryReferenceService.create(dto);
        return Result.ok();
    }

    @PostMapping("/update")
    @ApiOperation("编辑岗位薪资参考")
    public Result update(@Valid @RequestBody PositionSalaryReferenceUpdateDto dto) {
        salaryReferenceService.update(dto);
        return Result.ok();
    }

    @PostMapping("/delete")
    @ApiOperation("批量删除岗位薪资参考")
    public Result delete(@ApiParam(value = "业务ID列表", required = true) @RequestBody List<String> bids) {
        salaryReferenceService.batchDelete(bids);
        return Result.ok();
    }

    @PostMapping("/page")
    @ApiOperation("分页查询岗位薪资参考")
    public Result<PageResult<PositionSalaryReferenceVO>> findByPage(@RequestBody PositionSalaryReferenceQueryDto queryDto) {
        return Result.ok(salaryReferenceService.findByPage(queryDto));
    }

    @PostMapping("/export")
    @ApiOperation("导出岗位薪资参考")
    public void export(@RequestBody PositionSalaryReferenceQueryDto queryDto, HttpServletResponse response) throws IOException {
        salaryReferenceService.exportExcel(queryDto, response);
    }

    @PostMapping("/import")
    @ApiOperation("导入岗位薪资参考")
    public Result<String> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("progress") String progress) throws IOException {
        return Result.ok(salaryReferenceService.importExcel(file, progress));
    }


    @GetMapping("/template")
    @ApiOperation("下载导入模板")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        salaryReferenceService.downloadTemplate(response);
    }
}

package com.caidaocloud.vms.application.dto;

import com.caidaocloud.vms.domain.base.enums.VmsRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色初始化DTO
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@Data
@ApiModel(description = "角色初始化信息")
public class RoleInitDto {
    
    @ApiModelProperty(value = "租户ID", required = true)
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;


    @ApiModelProperty(value = "需要初始化的角色列表", required = true)
    @NotNull(message = "角色列表不能为空")
    private List<RoleDto> roles = new ArrayList<>();

    public void addRole(VmsRole role) {
        roles.add(new RoleDto(role.name(), role.getText()));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static
    class RoleDto {
        private String code;
        private String name;
    }
}

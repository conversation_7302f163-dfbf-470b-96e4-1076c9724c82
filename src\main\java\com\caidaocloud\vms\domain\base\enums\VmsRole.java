package com.caidaocloud.vms.domain.base.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 * VMS角色枚举
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
public enum VmsRole {
    /**
     * 用工方
     */
    EMPLOYER(0, "用工方"),

    /**
     * 供应商
     */
    SUPPLIER(1, "供应商"),

    /**
     * 面试官
     */
    INTERVIEWER(2, "面试官");

    private final int code;
    private final String text;

    VmsRole(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(this.code));
        return enumSimple;
    }

    public static VmsRole fromCode(int code) {
        for (VmsRole role : VmsRole.values()) {
            if (role.getCode() == code) {
                return role;
            }
        }
        throw new ServerException("Invalid VmsRole code: " + code);
    }

    public static VmsRole fromValue(EnumSimple value) {
        if (value == null || value.getValue() == null) {
            return null;
        }
        int code = Integer.parseInt(value.getValue());
        return fromCode(code);
    }
}

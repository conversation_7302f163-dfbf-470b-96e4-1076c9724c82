package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceQueryDto;
import com.caidaocloud.vms.domain.project.entity.PositionSalaryReference;

import java.util.List;

/**
 * 岗位薪资参考仓储接口
 *
 * @date 2026/01/08
 */
public interface PositionSalaryReferenceRepository {

    /**
     * 保存或更新
     */
    void saveOrUpdate(PositionSalaryReference entity);

    /**
     * 根据bid查询
     */
    PositionSalaryReference findByBid(String bid);

    /**
     * 根据bid删除
     */
    void deleteByBid(String bid);

    /**
     * 分页查询
     */
    PageResult<PositionSalaryReference> findByPage(PositionSalaryReferenceQueryDto queryDto);

    /**
     * 查询所有（用于导出）
     */
    List<PositionSalaryReference> findAll(PositionSalaryReferenceQueryDto queryDto);

    /**
     * 批量保存
     */
    void batchSave(List<PositionSalaryReference> entities);

    /**
     * 批量删除
     */
    void batchDelete(List<String> bids);
}

package com.caidaocloud.vms.domain.project.aspect;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.service.ProjectDraftService;
import com.caidaocloud.vms.application.service.ProjectHistoryService;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.context.HistoryContext;
import com.caidaocloud.vms.domain.project.entity.*;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.project.history.DataSimpleHistoryFormat;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * Repository工作流AOP切面
 * 统一拦截Repository的save和update方法，处理工作流逻辑
 *
 * <AUTHOR> Zhou
 * @date 2025/9/25
 */
@Aspect
@Component
@Slf4j
public class HistoryAspect {

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ProjectHistoryService projectHistoryService;

    @Autowired
    private ProjectSettingRepository projectSettingRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectDraftService projectDraftService;

    @Autowired
    private ProjectHistoryFactory projectHistoryFactory;




    /**
     * 环绕通知：拦截标记了@HistoryRecord的Application层方法
     */
    @Around("@annotation(historyRecord)")
    public Object handleHistoryRecord(ProceedingJoinPoint joinPoint, HistoryRecord historyRecord)
            throws Throwable {
        log.debug("HistoryAspect: 拦截到Application方法 {}", joinPoint.getSignature().getName());
        try {
            Object entity = fetchEntityParameter(joinPoint, historyRecord.dtoTypes());
            if (entity == null) {
                log.debug("HistoryAspect: 未找到支持的dto参数，直接执行原方法");
                return joinPoint.proceed();
            }

            HistoryContext.HistoryContextInfo historyContextInfo = initHistoryContext(entity, historyRecord);
            try {
                // 执行原方法
                Object result = joinPoint.proceed();
                return result;
            } finally {
                // 清理工作流上下文
                HistoryContext.clear();
            }

        } catch (Exception e) {
            log.error("HistoryAspect: Application层历史记录处理失败", e);
            // 清理上下文
            HistoryContext.clear();
            throw e;
        }
    }

    public Object fetchEntityParameter(ProceedingJoinPoint joinPoint, Class<?>[] supportedTypes) {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args.length == 0) {
            return null;
        }

        // 查找实体参数
        Object dto = findEntityParameter(args, supportedTypes);
        if (dto == null) {
            return null;
        }
        return dto;
    }

    private HistoryContext.HistoryContextInfo initHistoryContext(Object dto, HistoryRecord historyRecord) {
        String projectId = null;
        String positionId = null;
        boolean postWorkflowEnabled = false;
        if (historyRecord.historyType() == HistoryType.BASIC_INFO
                && historyRecord.operationType() == OperationType.CREATE) {
        } else {
            Map map = FastjsonUtil.convertObject(dto, Map.class);
            projectId = historyRecord.historyType() == HistoryType.BASIC_INFO ? (String) map.get("bid")
                    : (String) map.get("projectId");
            if (historyRecord.historyType() == HistoryType.POSITION) {
                positionId  = historyRecord.subType() == HistoryType.BASIC_INFO ? (String) map.get("bid")
                        : (String) map.get("positionId");
            }

            postWorkflowEnabled = checkPostWorkflowEnable(projectId);
        }
        HistoryContext.HistoryContextInfo historyContextInfo = new HistoryContext.HistoryContextInfo(projectId,
                historyRecord.historyType(), null, historyRecord.operationType(), checkProjectWorkflowEnable(),
                postWorkflowEnabled);
        historyContextInfo.setPostWorkflowEnabled(postWorkflowEnabled);
        historyContextInfo.setWorkflowEnabled(checkProjectWorkflowEnable());
        historyContextInfo.setPositionId(positionId);
        HistoryContext.setHistoryContextInfo(historyContextInfo);
        return historyContextInfo;
    }

    private boolean checkPostWorkflowEnable(String projectId) {
        Optional<ProjectSetting> setting = projectSettingRepository.getByProjectId(projectId);
        if (!setting.isPresent()) {
            return false;
        }
        return setting.get().getPositionApprovalFlow();
    }

    private boolean checkProjectWorkflowEnable() {
        return workflowService.checkWorkflowEnable();
    }

    /**
     * 环绕通知：拦截标记了@HistoryDetailRecord的Repository方法
     */
    @Around("@annotation(historyDetailRecord)")
    public Object handleHistoryDetailRecord(ProceedingJoinPoint joinPoint, HistoryDetailRecord historyDetailRecord)
            throws Throwable {
        log.debug("HistoryAspect: 拦截到Repository方法 {}", joinPoint.getSignature().getName());
        if (HistoryContext.isEmpty()) {
            return joinPoint.proceed();
        }
        OperationType operationType = historyDetailRecord.operationType();
        DataSimple entity = (DataSimple) fetchEntityParameter(joinPoint, historyDetailRecord.entityTypes());
        if (entity == null) {
            log.debug("HistoryAspect: 未找到支持的dto参数，直接执行原方法");
            return joinPoint.proceed();
        }

        try {
            Optional<DataSimple> originalEntity = fetchOriginalEntity(entity);
            OperationType operation = determineOperation(historyDetailRecord.operationType(), originalEntity);
            joinPoint.proceed();
            if (historyDetailRecord.historyType() == HistoryType.BASIC_INFO && operation.isCreate()) {
                HistoryContext.getHistoryContextInfo().setProjectId(entity.getBid());
            }
            if (historyDetailRecord.historyType() == HistoryType.POSITION && historyDetailRecord.subType()==HistoryType.BASIC_INFO && operation.isCreate()) {
                HistoryContext.getHistoryContextInfo().setPositionId(entity.getBid());
            }

            // 不进行流程，在保存后生成历史。开启流程时，生成draft
            HistoryContext.HistoryContextInfo info = HistoryContext.getHistoryContextInfo();
            ProjectDraft draft = ProjectHistoryFactory.createDraft(info.getProjectId(), info.getPositionId(), historyDetailRecord.historyType(), historyDetailRecord.subType(), operation, originalEntity, entity);
            if (!HistoryContext.getHistoryContextInfo().isWorkflowEnabled()) {
                ProjectHistoryDetail detail = projectHistoryFactory.generateHistoryDetailFromDraft(((DataSimpleHistoryFormat) originalEntity.orElse(null)), (DataSimpleHistoryFormat) entity, operation, draft);
                detail.setProjectId(info.getProjectId());
                detail.setApproveStatus(ApprovalStatus.APPROVED.toEnumSimple());
                // TODO: 2025/11/11 加载txt值，如company
                // detail.generateChangeSummary((DataSimpleHistoryFormat)entity, operation);
                projectHistoryService.saveDetail(detail);
            } else {
                projectDraftService.saveDraft(draft);
                log.info("草稿保存成功，targetId: {}, projectId: {}, type: {}, operation: {}",
                        draft.getTargetId(), draft.getProjectId(), draft.getType(), operationType);
            }

            // TODO: 2025/10/9 增加校验，注解只能加在void方法上
            return null;
        } catch (Exception e) {
            log.error("HistoryAspect: Repository层历史记录处理失败", e);
            throw e;
        }
    }

    private OperationType determineOperation(OperationType operationType, Optional<DataSimple> originalEntity) {
        switch (operationType) {
            case CREATE_OR_UPDATE:
                return originalEntity.isPresent() ? OperationType.UPDATE : OperationType.CREATE;
            case CREATE:
            case UPDATE:
            case DELETE:
                return operationType;
            case COMMIT:
            default:
                throw new ServerException("Operation type not support,type=" + operationType);
        }
    }

    /**
     * 查找实体参数
     */
    private Object findEntityParameter(Object[] args, Class<?>[] supportedTypes) {
        if (supportedTypes.length == 0) {
            // 如果没有指定支持的类型，返回第一个参数
            return args.length > 0 ? args[0] : null;
        }

        for (Object arg : args) {
            if (arg != null) {
                Class<?> argClass = arg.getClass();
                for (Class<?> supportedType : supportedTypes) {
                    if (supportedType.isAssignableFrom(argClass)) {
                        return arg;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取原始实体数据
     */
    private Optional<DataSimple> fetchOriginalEntity(DataSimple entity) {
        DataSimple dataSimple = entity;
        String identifier = dataSimple.getIdentifier();
        String bid = dataSimple.getBid();
        if (bid == null) {
            // 数据新增
            return Optional.empty();
        }
        DataSimple origin = DataQuery.identifier(identifier).one(bid, entity.getClass());
        return Optional.of(origin);
    }

}

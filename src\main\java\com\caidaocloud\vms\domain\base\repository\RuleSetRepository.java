package com.caidaocloud.vms.domain.base.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.RuleSetQueryDto;
import com.caidaocloud.vms.domain.base.entity.RuleSet;

import java.util.List;
import java.util.Optional;

/**
 * 规则集仓储接口
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
public interface RuleSetRepository {
    
    /**
     * 保存或更新规则集
     * 
     * @param ruleSet 规则集实体
     */
    void saveOrUpdate(RuleSet ruleSet);
    
    /**
     * 根据ID获取规则集
     * 
     * @param id 规则集ID
     * @return 规则集实体
     */
    Optional<RuleSet> getById(String id);
    
    /**
     * 根据名称获取规则集
     * 
     * @param name 规则集名称
     * @return 规则集实体
     */
    Optional<RuleSet> getByName(String name);
    
    /**
     * 分页查询规则集
     * 
     * @param queryDto 查询条件
     * @return 分页结果
     */
    PageResult<RuleSet> findByPage(RuleSetQueryDto queryDto);
    
    /**
     * 获取所有启用的规则集
     * 
     * @return 规则集列表
     */
    List<RuleSet> findAllEnabled();
    
    /**
     * 根据ID删除规则集（软删除）
     * 
     * @param id 规则集ID
     */
    void deleteById(String id);
    
    /**
     * 检查名称是否已存在
     * 
     * @param name 规则集名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByName(String name, String excludeId);
}

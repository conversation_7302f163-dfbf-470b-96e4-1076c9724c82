package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.vms.domain.project.entity.PositionRequirement;

import java.util.List;
import java.util.Optional;

public interface PositionRequirementRepository {

    /**
     * 保存或更新岗位招聘要求
     * @param positionRequirement 岗位招聘要求
     * @return 要求ID
     */
    void saveOrUpdate(PositionRequirement positionRequirement);

    /**
     * 根据岗位ID获取招聘要求
     * @param positionId 岗位ID
     * @return 招聘要求
     */
    Optional<PositionRequirement> getByPositionId(String positionId);

    /**
     * 删除岗位招聘要求
     * @param positionId 岗位ID
     */
    void deleteByPositionId(String positionId);

    void init(PositionRequirement positionRequirement, String positionId);

	List<PositionRequirement> listByPositionIds(List<String> positionIds);
}

package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.PositionSupplierQueryDto;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.PositionSupplierRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class PositionSupplierRepositoryImpl implements PositionSupplierRepository {

    @Override
    @HistoryDetailRecord(entityTypes = { PositionSupplier.class }, historyType = HistoryType.POSITION,subType = HistoryType.SUPPLIER,operationType = OperationType.CREATE)
    public String saveOrUpdate(PositionSupplier positionSupplier) {
        if (positionSupplier.getBid() == null) {
            DataInsert.identifier(PositionSupplier.identifier).insert(positionSupplier);
        } else {
            DataUpdate.identifier(PositionSupplier.identifier).update(positionSupplier);
        }
        return positionSupplier.getBid();
    }

    @Override
    public List<PositionSupplier> getByPositionId(String positionId) {
        return DataQuery.identifier(PositionSupplier.identifier).limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("positionId", positionId), PositionSupplier.class)
                .getItems();
    }

    @Override
    public List<PositionSupplier> getByQuery(PositionSupplierQueryDto queryDto) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("positionId", queryDto.getPositionId());

        // 添加应邀状态过滤条件
        if (queryDto.getInviteStatus() != null) {
            filter = filter.and(DataFilter.eq("inviteStatus.value", String.valueOf(queryDto.getInviteStatus())));
        }

        // TODO: 模糊查询供应商名称的实现在repository层留todo
        // 这里需要联合查询供应商表，根据供应商名称进行模糊查询
        // 由于当前框架限制，暂时无法实现跨表查询，需要在Service层处理

        return DataQuery.identifier(PositionSupplier.identifier).limit(-1, 1)
                .filter(filter, PositionSupplier.class).getItems();
    }

    @Override
    public Optional<PositionSupplier> getById(String relationId) {
        return Optional.ofNullable(DataQuery.identifier(PositionSupplier.identifier)
                .oneOrNull(relationId, PositionSupplier.class));
    }

    @Override
    public void deleteRelation(PositionSupplier positionSupplier) {
        DataDelete.identifier(PositionSupplier.identifier).delete(positionSupplier.getBid());
    }

    @Override
    public void deleteByPositionId(String positionId) {
        List<PositionSupplier> relations = getByPositionId(positionId);
        for (PositionSupplier relation : relations) {
            DataDelete.identifier(PositionSupplier.identifier).delete(relation.getBid());
        }
    }

    @Override
    public boolean existsByPositionIdAndSupplierId(String positionId, String supplierId) {
        return DataQuery.identifier(PositionSupplier.identifier)
                .filter(DataFilter.eq("positionId", positionId)
                        .andEq("supplierId", supplierId)
                        .andNe("deleted", Boolean.TRUE.toString()), PositionSupplier.class)
                .getTotal() > 0;
    }

    @Override
    public List<PositionSupplier> listBySupplierId(String supplierId) {
        return DataQuery.identifier(PositionSupplier.identifier)
                .filter(DataFilter.eq("supplierId", supplierId)
                        .andNe("deleted", Boolean.TRUE.toString()), PositionSupplier.class).getItems();
    }

    @Override
    public List<PositionSupplier> listByProjectIdSupplierId(String projectId, String supplierId) {
        return DataQuery.identifier(PositionSupplier.identifier)
                .filter(DataFilter.eq("supplierId", supplierId)
                        .andEq("projectId", projectId)
                        .andNe("deleted", Boolean.TRUE.toString()), PositionSupplier.class).getItems();
    }

    @Override
    public List<PositionSupplier> listByPositionIds(List<String> positionIds) {
        return DataQuery.identifier(PositionSupplier.identifier).limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andIn("positionId", positionIds), PositionSupplier.class)
                .getItems();
    }

}

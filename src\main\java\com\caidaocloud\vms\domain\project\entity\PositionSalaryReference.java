package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位薪资参考实体类
 * 存储不同城市、行业、岗位、经验的薪酬分位数据
 */
@Data
public class PositionSalaryReference extends BaseEntity {

    public static String identifier = "entity.vms.PositionSalaryReference";

    /**
     * 年份
     */
    private Integer year;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 行业编码
     */
    private String industryCode;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 岗位名称
     */
    private String positionName;

    /**
     * 经验要求名称（如：3-5年、5-8年）
     */
    private String experienceName;

    /**
     * 25分位薪资
     */
    private String percentile25;

    /**
     * 50分位薪资
     */
    private String percentile50;

    /**
     * 75分位薪资
     */
    private String percentile75;

    /**
     * 90分位薪资
     */
    private String percentile90;

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public PositionSalaryReference(){
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
        setDataStartTime(0L);
        setDataEndTime(com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.MAX_TIMESTAMP);
    }
}

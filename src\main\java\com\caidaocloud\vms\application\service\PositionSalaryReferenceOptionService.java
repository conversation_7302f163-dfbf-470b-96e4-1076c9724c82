package com.caidaocloud.vms.application.service;

import com.caidaocloud.vms.application.vo.CascadeOptionVO;
import com.caidaocloud.vms.infrastructure.feign.IHrPaasFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 岗位薪资参考选项服务
 *
 * @date 2026/01/07
 */
@Slf4j
@Service
public class PositionSalaryReferenceOptionService {

    @Autowired
    private IHrPaasFeignClient hrPaasFeignClient;

    /**
     * 获取行业级联选项
     */
    public List<CascadeOptionVO> getIndustryOptions() {
        List<CascadeOptionVO> industries = new ArrayList<>();

        industries.add(new CascadeOptionVO("MANUFACTURING", "制造业/工业", Arrays.asList(
                new CascadeOptionVO("MANUFACTURING_AUTO", "汽车"),
                new CascadeOptionVO("MANUFACTURING_MACHINERY", "机械"),
                new CascadeOptionVO("MANUFACTURING_CHEMICAL", "化工"),
                new CascadeOptionVO("MANUFACTURING_ELECTRONICS", "电子"),
                new CascadeOptionVO("MANUFACTURING_PACKAGING", "消费品包装")
        )));

        industries.add(new CascadeOptionVO("CONSUMER_RETAIL", "消费品与零售", Arrays.asList(
                new CascadeOptionVO("CONSUMER_FMCG", "快消品（FMCG）"),
                new CascadeOptionVO("CONSUMER_LUXURY", "奢侈品"),
                new CascadeOptionVO("CONSUMER_RETAIL_STORE", "零售")
        )));

        industries.add(new CascadeOptionVO("FINANCIAL", "金融服务业", Arrays.asList(
                new CascadeOptionVO("FINANCIAL_BANK", "银行"),
                new CascadeOptionVO("FINANCIAL_INSURANCE", "保险"),
                new CascadeOptionVO("FINANCIAL_SECURITIES", "证券"),
                new CascadeOptionVO("FINANCIAL_ASSET", "资管"),
                new CascadeOptionVO("FINANCIAL_FINTECH", "金融科技（FinTech）")
        )));

        industries.add(new CascadeOptionVO("TECH_INTERNET", "高科技与互联网", Arrays.asList(
                new CascadeOptionVO("TECH_SOFTWARE", "软件与互联网"),
                new CascadeOptionVO("TECH_SEMICONDUCTOR", "半导体与硬件"),
                new CascadeOptionVO("TECH_TELECOM", "通信设备与服务")
        )));

        industries.add(new CascadeOptionVO("LIFE_SCIENCE", "生命科学与医疗健康", Arrays.asList(
                new CascadeOptionVO("LIFE_PHARMA", "制药"),
                new CascadeOptionVO("LIFE_BIOTECH", "生物技术"),
                new CascadeOptionVO("LIFE_MEDICAL_DEVICE", "医疗器械"),
                new CascadeOptionVO("LIFE_MEDICAL_SERVICE", "医疗服务")
        )));

        industries.add(new CascadeOptionVO("ENERGY", "能源与公用事业", Arrays.asList(
                new CascadeOptionVO("ENERGY_OIL_GAS", "石油天然气"),
                new CascadeOptionVO("ENERGY_POWER", "电力"),
                new CascadeOptionVO("ENERGY_RENEWABLE", "新能源（太阳能、风能）")
        )));

        industries.add(new CascadeOptionVO("REAL_ESTATE", "房地产与建筑工程", Arrays.asList(
                new CascadeOptionVO("REAL_ESTATE_DEV", "房地产开发"),
                new CascadeOptionVO("REAL_ESTATE_DESIGN", "建筑设计"),
                new CascadeOptionVO("REAL_ESTATE_CONSTRUCTION", "工程施工")
        )));

        industries.add(new CascadeOptionVO("AI_BIGDATA", "人工智能与大数据", null));
        industries.add(new CascadeOptionVO("NEV_AUTONOMOUS", "新能源汽车与智能驾驶", null));
        industries.add(new CascadeOptionVO("CLOUD_SECURITY", "云计算与网络安全", null));
        industries.add(new CascadeOptionVO("BIO_GENE", "生物医药与基因技术", null));
        industries.add(new CascadeOptionVO("ESG", "ESG与可持续发展", null));

        return industries;
    }

    /**
     * 获取城市级联选项（省-市）
     */
    public List<CascadeOptionVO> getCityOptions() {
        Result result = hrPaasFeignClient.getAddressTree();

        if (result == null || !result.isSuccess() || result.getData() == null) {
            log.warn("获取地址树数据失败");
            return new ArrayList<>();
        }

        return convertToCascadeOptions(result.getData());
    }

    /**
     * 批量根据城市名称获取城市code
     */
    public Map<String, String> getCityCodesByNames(List<String> cityNames) {
        Map<String, String> resultMap = new HashMap<>();
        if (cityNames == null || cityNames.isEmpty()) {
            return resultMap;
        }

        List<CascadeOptionVO> cityOptions = getCityOptions();
        for (String cityName : cityNames) {
            for (CascadeOptionVO province : cityOptions) {
                if (cityName.equals(province.getName())) {
                    resultMap.put(cityName, province.getCode());
                    break;
                }
                if (province.getChildren() != null) {
                    for (CascadeOptionVO city : province.getChildren()) {
                        if (cityName.equals(city.getName())) {
                            resultMap.put(cityName, city.getCode());
                            break;
                        }
                    }
                }
                if (resultMap.containsKey(cityName)) {
                    break;
                }
            }
        }
        return resultMap;
    }

    /**
     * 批量根据行业名称获取行业code
     */
    public Map<String, String> getIndustryCodesByNames(List<String> industryNames) {
        Map<String, String> resultMap = new HashMap<>();
        if (industryNames == null || industryNames.isEmpty()) {
            return resultMap;
        }

        List<CascadeOptionVO> industries = getIndustryOptions();
        for (String industryName : industryNames) {
            for (CascadeOptionVO industry : industries) {
                if (industryName.equals(industry.getName())) {
                    resultMap.put(industryName, industry.getCode());
                    break;
                }
                if (industry.getChildren() != null) {
                    for (CascadeOptionVO sub : industry.getChildren()) {
                        if (industryName.equals(sub.getName())) {
                            resultMap.put(industryName, sub.getCode());
                            break;
                        }
                    }
                }
                if (resultMap.containsKey(industryName)) {
                    break;
                }
            }
        }
        return resultMap;
    }

    /**
     * 只取省和市两级
     */
    @SuppressWarnings("unchecked")
    private List<CascadeOptionVO> convertToCascadeOptions(Object data) {
        List<CascadeOptionVO> result = new ArrayList<>();

        if (!(data instanceof List)) {
            return result;
        }

        List<Map<String, Object>> provinceList = (List<Map<String, Object>>) data;

        for (Map<String, Object> province : provinceList) {
            Map<String, Object> provinceData = (Map<String, Object>) province.get("data");
            if (provinceData == null) {
                continue;
            }
            
            String provinceCode = String.valueOf(provinceData.get("code"));
            String provinceName = String.valueOf(provinceData.get("name"));

            List<CascadeOptionVO> cityOptions = new ArrayList<>();

            // 获取市级数据
            Object children = province.get("children");
            if (children instanceof List) {
                List<Map<String, Object>> cityList = (List<Map<String, Object>>) children;
                for (Map<String, Object> city : cityList) {
                    Map<String, Object> cityData = (Map<String, Object>) city.get("data");
                    if (cityData == null) {
                        continue;
                    }
                    String cityCode = String.valueOf(cityData.get("code"));
                    String cityName = String.valueOf(cityData.get("name"));
                    cityOptions.add(new CascadeOptionVO(cityCode, cityName));
                }
            }

            result.add(new CascadeOptionVO(provinceCode, provinceName, cityOptions.isEmpty() ? null : cityOptions));
        }

        return result;
    }
}

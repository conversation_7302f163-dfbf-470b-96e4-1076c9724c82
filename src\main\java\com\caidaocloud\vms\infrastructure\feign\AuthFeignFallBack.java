package com.caidaocloud.vms.infrastructure.feign;

import com.caidaocloud.vms.application.dto.RoleInitDto;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Auth服务Feign客户端降级处理
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@Slf4j
@Component
public class AuthFeignFallBack implements IAuthFeignClient {

    @Override
    public Result<String> initTenantRole(RoleInitDto roleInitDto) {
        log.error("调用auth-service初始化租户角色失败，租户ID: {}, 租户名称: {}", 
                roleInitDto.getTenantId(), roleInitDto.getTenantName());
        return Result.fail("调用auth-service初始化租户角色失败");
    }
}

package com.caidaocloud.vms.application.service;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.vms.application.vo.PositionRequirementVO;
import com.caidaocloud.vms.application.vo.PositionSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.googlecode.totallylazy.Sequences;
import org.jetbrains.annotations.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 岗位工作流服务
 * 处理岗位审批流程相关的业务逻辑
 *
 * <AUTHOR>
 * @date 2025/12/26
 */
@Service
public class PositionWorkflowService {
    
    @Autowired
    private ProjectHistoryService projectHistoryService;
    
    @Autowired
    private ProjectPositionService projectPositionService;

    /**
     * 审批流程岗位详情查询
     * 根据businessKey查询审批流程中的岗位信息详情
     *
     * @param businessKey 业务键
     * @return 审批流程岗位详情信息
     */
    public WorkflowDetailVO<ProjectPositionVO> loadWorkflowPositionDetail(String businessKey) {
        // 根据businessKey查询历史记录
        ProjectHistory history = loadHistory(businessKey);
        String historyId = history.getBid();
        String projectId = history.getProjectId();
        String positionId = history.getPositionId();

        // 查询历史详情记录
        List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId, HistoryType.POSITION);

        WorkflowDetailVO<ProjectPositionVO> result = new WorkflowDetailVO<>();
        result.setHistoryId(historyId);
        result.setProjectId(projectId);
        result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
        
        // 如果有具体的岗位ID，则查询单个岗位详情，否则查询项目下所有岗位
        ProjectPositionVO positionDetail = projectPositionService.getPositionDetailTxt(positionId);
        positionDetail.setPosition(positionDetail.getPositionTxt());
        positionDetail.setOrganization(positionDetail.getOrganizeTxt());
        positionDetail.setCompany(positionDetail.getCompanyTxt());
        result.setDetail(positionDetail);
        return result;
    }

    /**
     * 根据businessKey加载历史记录
     *
     * @param businessKey 业务键
     * @return 历史记录
     */
    @NotNull
    private ProjectHistory loadHistory(String businessKey) {
        // 根据businessKey查询历史记录
        Optional<ProjectHistory> historyOpt = projectHistoryService.loadByBusinessKey(businessKey);
        if (!historyOpt.isPresent()) {
            throw new ServerException("审批记录不存在: " + businessKey);
        }

        ProjectHistory history = historyOpt.get();

        return history;
    }

    public WorkflowDetailVO<PositionRequirementVO> loadWorkflowPositionRequirementDetail(String businessKey) {
        // 根据businessKey查询历史记录
        ProjectHistory history = loadHistory(businessKey);
        String historyId = history.getBid();
        String projectId = history.getProjectId();
        String positionId = history.getPositionId();

        // 查询历史详情记录
        List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId, HistoryType.REQUIRE);

        WorkflowDetailVO<PositionRequirementVO> result = new WorkflowDetailVO<>();
        result.setHistoryId(historyId);
        result.setProjectId(projectId);
        result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
        result.setDetail(projectPositionService.getPositionRequirement(positionId));
        return result;
    }

    public WorkflowDetailVO<List<PositionSupplierVO>> loadWorkflowPositionSupplierDetail(String businessKey) {
        // 根据businessKey查询历史记录
        ProjectHistory history = loadHistory(businessKey);
        String historyId = history.getBid();
        String projectId = history.getProjectId();
        String positionId = history.getPositionId();

        // 查询历史详情记录
        List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId, HistoryType.SUPPLIER);

        WorkflowDetailVO<List<PositionSupplierVO>> result = new WorkflowDetailVO<>();
        result.setHistoryId(historyId);
        result.setProjectId(projectId);
        result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
        result.setDetail(projectPositionService.getPositionSuppliers(positionId));
        return result;
    }
}

package com.caidaocloud.vms.application.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.ProjectPositionQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectContactVO;
import com.caidaocloud.vms.application.vo.ProjectPositionPageVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.application.vo.ProjectSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.application.vo.SupplierDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowContactDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowPositionDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowSettingDetailVO;
import com.caidaocloud.vms.application.vo.WorkflowSupplierDetailVO;
import com.caidaocloud.vms.application.vo.workflow.WorkflowProjectVO;
import com.caidaocloud.vms.domain.project.entity.ProjectContact;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.googlecode.totallylazy.Sequences;
import org.jetbrains.annotations.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/12/26
 */
@Service
public class ProjectWorkflowService {
	@Autowired
	private ProjectHistoryService projectHistoryService;
	@Autowired
	private ProjectService projectService;
	@Autowired
	private ProjectPositionService projectPositionService;
	@Autowired
	private ProjectContactService projectContactService;
	@Autowired
	private ProjectSupplierService projectSupplierService;
	@Autowired
	private ProjectSettingService projectSettingService;
	/**
	 * 审批流程项目详情查询
	 * 根据businessKey查询审批流程中的项目基本信息详情
	 *
	 * @param businessKey 业务键
	 * @return 审批流程项目详情信息
	 */
	public WorkflowDetailVO<WorkflowProjectVO> loadWorkflowProjectDetail(String businessKey) {
		ProjectHistory history = loadHistory(businessKey);
		String historyId = history.getBid();
		String projectId = history.getProjectId();

		// 查询历史详情记录
		List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId, HistoryType.BASIC_INFO);

		WorkflowDetailVO<WorkflowProjectVO> result = new WorkflowDetailVO();
		result.setHistoryId(historyId);
		result.setProjectId(projectId);
		result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
		result.setDetail(projectService.loadProjectTxt(projectId));

		return result;
	}

	@NotNull
	private ProjectHistory loadHistory(String businessKey) {
		// 根据businessKey查询历史记录
		Optional<ProjectHistory> historyOpt = projectHistoryService.loadByBusinessKey(businessKey);
		if (!historyOpt.isPresent()) {
			throw new ServerException("审批记录不存在: " + businessKey);
		}

		return historyOpt.get();
	}

	/**
	 * 审批流程岗位详情查询
	 * 根据businessKey查询审批流程中的岗位信息详情
	 *
	 * @param businessKey 业务键
	 * @return 审批流程岗位详情信息
	 */
	public WorkflowDetailVO<List<ProjectPositionVO>> loadWorkflowPositionDetail(String businessKey) {
		// 根据businessKey查询历史记录
		ProjectHistory history = loadHistory(businessKey);
		String historyId = history.getBid();
		String projectId = history.getProjectId();
		String positionId = history.getPositionId();

		// 查询历史详情记录
		List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId,HistoryType.POSITION);


		WorkflowDetailVO<List<ProjectPositionVO>> result = new WorkflowDetailVO();
		result.setHistoryId(historyId);
		result.setProjectId(projectId);
		result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
		result.setDetail(loadPositionList(projectId));

		return result;
	}

	private List<ProjectPositionVO> loadPositionList(String projectId) {
		ProjectPositionQueryDTO projectPositionQueryDTO = new ProjectPositionQueryDTO();
		projectPositionQueryDTO.setPageSize(-1);
		projectPositionQueryDTO.setProjectId(projectId);

		PageResult<ProjectPositionPageVO> page = projectPositionService.getPositionPage(projectPositionQueryDTO);
		return Sequences.sequence(page.getItems()).map(i -> {
			ProjectPositionVO vo = ObjectConverter.convert(i, ProjectPositionVO.class);
			vo.setPosition(i.getPositionTxt());
			vo.setOrganization(i.getOrganizationTxt());
			vo.setCompany(i.getCompanyTxt());
			return vo;
		}).toList();
	}

	/**
	 * 审批流程供应商详情查询
	 * 根据businessKey查询审批流程中的供应商信息详情
	 *
	 * @param businessKey 业务键
	 * @return 审批流程供应商详情信息
	 */
	public WorkflowDetailVO<List<ProjectSupplierVO>>  loadWorkflowSupplierDetail(String businessKey) {
		// 根据businessKey查询历史记录
		ProjectHistory history = loadHistory(businessKey);
		String historyId = history.getBid();
		String projectId = history.getProjectId();

		// 查询历史详情记录
		List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId,HistoryType.SUPPLIER);


		WorkflowDetailVO<List<ProjectSupplierVO>> result = new WorkflowDetailVO();
		result.setHistoryId(historyId);
		result.setProjectId(projectId);
		result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
		result.setDetail(projectSupplierService.getProjectSuppliersList(projectId));

		return result;
	}

	/**
	 * 审批流程联系人详情查询
	 * 根据businessKey查询审批流程中的联系人信息详情
	 *
	 * @param businessKey 业务键
	 * @return 审批流程联系人详情信息
	 */
	public WorkflowDetailVO<List<ProjectContactVO>> loadWorkflowContactDetail(String businessKey) {
		// 根据businessKey查询历史记录
		ProjectHistory history = loadHistory(businessKey);
		String historyId = history.getBid();
		String projectId = history.getProjectId();


		// 查询历史详情记录
		List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId,HistoryType.CONTACT);


		WorkflowDetailVO<List<ProjectContactVO>> result = new WorkflowDetailVO();
		result.setHistoryId(historyId);
		result.setProjectId(projectId);
		result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
		result.setDetail(projectContactService.projectContactList(projectId));

		return result;
	}

	/**
	 * 审批流程设置详情查询
	 * 根据businessKey查询审批流程中的设置信息详情
	 *
	 * @param businessKey 业务键
	 * @return 审批流程设置详情信息
	 */
	public WorkflowDetailVO<ProjectSettingVO> loadWorkflowSettingDetail(String businessKey) {
		// 根据businessKey查询历史记录
		ProjectHistory history = loadHistory(businessKey);
		String historyId = history.getBid();
		String projectId = history.getProjectId();


		// 查询历史详情记录
		List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetailByType(historyId,HistoryType.SETTING);


		WorkflowDetailVO<ProjectSettingVO> result = new WorkflowDetailVO();
		result.setHistoryId(historyId);
		result.setProjectId(projectId);
		result.setChanges(Sequences.sequence(detailList).flatMap(ProjectHistoryDetail::getChange).toList());
		result.setDetail(projectSettingService.getByProjectId(projectId));
		return result;
	}
}

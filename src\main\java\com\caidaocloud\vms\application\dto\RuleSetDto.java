package com.caidaocloud.vms.application.dto;

import com.caidaocloud.vms.domain.base.enums.EmployeeManagementMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 规则集DTO
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@Data
@ApiModel(description = "规则集信息")
public class RuleSetDto {
    
    private String bid;

    @ApiModelProperty(value = "员工管理模式", required = true)
    @NotNull(message = "员工管理模式不能为空")
    private EmployeeManagementMode mode;
}

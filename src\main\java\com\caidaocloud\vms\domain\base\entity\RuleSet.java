package com.caidaocloud.vms.domain.base.entity;

import com.caidaocloud.vms.domain.base.enums.EmployeeManagementMode;
import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则集实体类
 * 用于管理系统参数配置
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleSet extends BaseEntity {

    /**
     * 规则集名称
     */
    @DisplayName("规则集名称")
    private String name;

    /**
     * 规则集描述
     */
    @DisplayName("规则集描述")
    private String description;

    /**
     * 员工管理模式
     */
    @DisplayName("员工管理模式")
    private EmployeeManagementMode mode;

    /**
     * 是否启用
     */
    @DisplayName("是否启用")
    private Boolean enabled = true;

    /**
     * 备注
     */
    @DisplayName("备注")
    private String remark;

    public static String identifier = "entity.vms.RuleSet";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    /**
     * 构造函数
     */
    public RuleSet() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param name 规则集名称
     * @param mode 员工管理模式
     */
    public RuleSet(String name, EmployeeManagementMode mode) {
        this();
        this.name = name;
        this.mode = mode;
    }
}

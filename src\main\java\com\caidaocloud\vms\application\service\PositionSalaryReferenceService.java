package com.caidaocloud.vms.application.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceCreateDto;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceExcelDto;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceQueryDto;
import com.caidaocloud.vms.application.dto.PositionSalaryReferenceUpdateDto;
import com.caidaocloud.vms.application.vo.ImportResultVO;
import com.caidaocloud.vms.application.vo.PositionSalaryReferenceVO;
import com.caidaocloud.vms.domain.project.entity.PositionSalaryReference;
import com.caidaocloud.vms.domain.project.repository.PositionSalaryReferenceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 岗位薪资参考服务
 *
 * @date 2026/01/08
 */
@Slf4j
@Service
public class PositionSalaryReferenceService {

    private static final String IMPORT_PROGRESS_KEY = "importDataList_";
    private static final String IMPORT_ERROR_KEY = "importErrorList_";

    @Autowired
    private PositionSalaryReferenceRepository repository;

    @Autowired
    private PositionSalaryReferenceOptionService optionService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 新增
     */
    public void create(PositionSalaryReferenceCreateDto dto) {
        PositionSalaryReference entity = new PositionSalaryReference();
        entity.setYear(dto.getYear());
        entity.setCityCode(dto.getCityCode());
        entity.setCityName(dto.getCityName());
        entity.setIndustryCode(dto.getIndustryCode());
        entity.setIndustryName(dto.getIndustryName());
        entity.setPositionName(dto.getPositionName());
        entity.setExperienceName(dto.getExperienceName());
        entity.setPercentile25(dto.getPercentile25());
        entity.setPercentile50(dto.getPercentile50());
        entity.setPercentile75(dto.getPercentile75());
        entity.setPercentile90(dto.getPercentile90());
        repository.saveOrUpdate(entity);
    }

    /**
     * 编辑
     */
    public void update(PositionSalaryReferenceUpdateDto dto) {
        PositionSalaryReference entity = repository.findByBid(dto.getBid());
        Assert.notNull(entity, "数据不存在");

        if (dto.getYear() != null) {
            entity.setYear(dto.getYear());
        }
        if (dto.getCityCode() != null) {
            entity.setCityCode(dto.getCityCode());
        }
        if (dto.getCityName() != null) {
            entity.setCityName(dto.getCityName());
        }
        if (dto.getIndustryCode() != null) {
            entity.setIndustryCode(dto.getIndustryCode());
        }
        if (dto.getIndustryName() != null) {
            entity.setIndustryName(dto.getIndustryName());
        }
        if (dto.getPositionName() != null) {
            entity.setPositionName(dto.getPositionName());
        }
        if (dto.getExperienceName() != null) {
            entity.setExperienceName(dto.getExperienceName());
        }
        if (dto.getPercentile25() != null) {
            entity.setPercentile25(dto.getPercentile25());
        }
        if (dto.getPercentile50() != null) {
            entity.setPercentile50(dto.getPercentile50());
        }
        if (dto.getPercentile75() != null) {
            entity.setPercentile75(dto.getPercentile75());
        }
        if (dto.getPercentile90() != null) {
            entity.setPercentile90(dto.getPercentile90());
        }

        entity.update();
        repository.saveOrUpdate(entity);
    }

    /**
     * 批量删除
     */
    public void batchDelete(List<String> bids) {
        repository.batchDelete(bids);
    }

    /**
     * 分页查询
     */
    public PageResult<PositionSalaryReferenceVO> findByPage(PositionSalaryReferenceQueryDto queryDto) {
        PageResult<PositionSalaryReference> pageResult = repository.findByPage(queryDto);

        List<PositionSalaryReferenceVO> voList = pageResult.getItems().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        PageResult<PositionSalaryReferenceVO> result = new PageResult<>();
        result.setItems(voList);
        result.setTotal(pageResult.getTotal());
        return result;
    }

    /**
     * Excel导出
     */
    public void exportExcel(PositionSalaryReferenceQueryDto queryDto, HttpServletResponse response) throws IOException {
        List<PositionSalaryReference> list = repository.findAll(queryDto);
        List<PositionSalaryReferenceExcelDto> excelList = list.stream()
                .map(this::convertToExcelDto)
                .collect(Collectors.toList());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("岗位薪资参考", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), PositionSalaryReferenceExcelDto.class)
                .sheet("岗位薪资参考")
                .doWrite(excelList);
    }

    /**
     * Excel导入（带进度和错误信息）
     */
    public String importExcel(MultipartFile file, String progressId) throws IOException {
        List<PositionSalaryReferenceExcelDto> list = EasyExcel.read(file.getInputStream())
                .head(PositionSalaryReferenceExcelDto.class)
                .sheet()
                .doReadSync();

        // 收集所有城市名称和行业名称，批量查询code
        List<String> cityNames = list.stream()
                .map(PositionSalaryReferenceExcelDto::getCityName)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<String> industryNames = list.stream()
                .map(PositionSalaryReferenceExcelDto::getIndustryName)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> cityCodeMap = optionService.getCityCodesByNames(cityNames);
        Map<String, String> industryCodeMap = optionService.getIndustryCodesByNames(industryNames);

        List<PositionSalaryReference> successList = new ArrayList<>();
        List<Map<String, Object>> errorMessages = new ArrayList<>();
        int rowNum = 1;

        for (PositionSalaryReferenceExcelDto dto : list) {
            rowNum++;

            // 校验必填字段
            if (dto.getYear() == null) {
                errorMessages.add(makeError(rowNum, "A", "年份不能为空", 1));
                continue;
            }
            if (StringUtils.isBlank(dto.getCityName())) {
                errorMessages.add(makeError(rowNum, "B", "城市不能为空", 1));
                continue;
            }
            if (StringUtils.isBlank(dto.getIndustryName())) {
                errorMessages.add(makeError(rowNum, "C", "行业不能为空", 1));
                continue;
            }
            if (StringUtils.isBlank(dto.getPositionName())) {
                errorMessages.add(makeError(rowNum, "D", "岗位不能为空", 1));
                continue;
            }
            if (StringUtils.isBlank(dto.getExperienceName())) {
                errorMessages.add(makeError(rowNum, "E", "经验要求不能为空", 1));
                continue;
            }

            // 根据名称获取code
            String cityCode = cityCodeMap.get(dto.getCityName());
            String industryCode = industryCodeMap.get(dto.getIndustryName());

            if (StringUtils.isBlank(cityCode)) {
                errorMessages.add(makeError(rowNum, "B", "城市名称无效：" + dto.getCityName(), 1));
                continue;
            }
            if (StringUtils.isBlank(industryCode)) {
                errorMessages.add(makeError(rowNum, "C", "行业名称无效：" + dto.getIndustryName(), 1));
                continue;
            }

            PositionSalaryReference entity = new PositionSalaryReference();
            entity.setYear(dto.getYear());
            entity.setCityCode(cityCode);
            entity.setCityName(dto.getCityName());
            entity.setIndustryCode(industryCode);
            entity.setIndustryName(dto.getIndustryName());
            entity.setPositionName(dto.getPositionName());
            entity.setExperienceName(dto.getExperienceName());
            entity.setPercentile25(dto.getPercentile25());
            entity.setPercentile50(dto.getPercentile50());
            entity.setPercentile75(dto.getPercentile75());
            entity.setPercentile90(dto.getPercentile90());
            successList.add(entity);
        }

        // 批量保存成功数据
        if (!successList.isEmpty()) {
            repository.batchSave(successList);
        }

        // 存储进度到Redis
        Map<String, Object> progressMap = new HashMap<>();
        progressMap.put("progress", 1);
        progressMap.put("total", list.size());
        progressMap.put("successCount", successList.size());
        progressMap.put("failCount", errorMessages.size());
        progressMap.put("finished", true);
        redisTemplate.opsForValue().set(IMPORT_PROGRESS_KEY + progressId, progressMap, 5, TimeUnit.MINUTES);

        // 存储错误信息到Redis
        if (!errorMessages.isEmpty()) {
            try {
                redisTemplate.opsForValue().set(IMPORT_ERROR_KEY + progressId, 
                        new ObjectMapper().writeValueAsString(errorMessages), 5, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("存储错误信息失败", e);
            }
        }

        return "导入完成，总数=" + list.size() + "，成功=" + successList.size() + "，失败=" + errorMessages.size();
    }


    /**
     * 构造错误信息
     */
    private Map<String, Object> makeError(int rowNum, String colNum, String message, int level) {
        Map<String, Object> error = new HashMap<>();
        error.put("rowNum", rowNum);
        error.put("colNum", colNum);
        error.put("message", message);
        error.put("level", level);
        return error;
    }

    /**
     * 下载导入模板
     */
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("岗位薪资参考导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), PositionSalaryReferenceExcelDto.class)
                .sheet("岗位薪资参考")
                .doWrite(new ArrayList<>());
    }

    private PositionSalaryReferenceVO convertToVO(PositionSalaryReference entity) {
        PositionSalaryReferenceVO vo = new PositionSalaryReferenceVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    private PositionSalaryReferenceExcelDto convertToExcelDto(PositionSalaryReference entity) {
        PositionSalaryReferenceExcelDto dto = new PositionSalaryReferenceExcelDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}

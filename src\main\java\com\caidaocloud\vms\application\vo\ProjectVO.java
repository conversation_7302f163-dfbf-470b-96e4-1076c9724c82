package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "项目详细信息")
public class ProjectVO {
    @ApiModelProperty(value = "项目ID", example = "123456")
    private String bid;

    @ApiModelProperty(value = "项目编码", example = "PRJ-2025-001")
    private String projectCode;

    @ApiModelProperty(value = "项目名称", example = "系统升级项目")
    private String projectName;

    @ApiModelProperty(value = "开始日期(时间戳)", example = "1714521600000")
    private Long startDate;

    @ApiModelProperty(value = "结束日期(时间戳)", example = "1746057600000")
    private Long endDate;

    @ApiModelProperty(value = "预算总额", example = "1000000.00")
    private BigDecimal totalBudget;

    @ApiModelProperty(value = "已用预算", example = "1000000.00")
    private BigDecimal usedBudget;

    @ApiModelProperty(value = "计划人员数量", example = "10")
    private Integer plannedHeadcount;
    @ApiModelProperty(value = "实际人员数量", example = "10")
    private Integer actualHeadcount;

    @ApiModelProperty(value = "项目负责人")
    private EmpSimple projectManager;

    @ApiModelProperty(value = "所属公司")
    private String company;

    @ApiModelProperty(value = "所属公司名称")
    private String companyTxt;

    @ApiModelProperty(value = "项目负责人姓名")
    private String projectManagerTxt;

    @ApiModelProperty(value = "项目负责人组织")
    private String projectManagerOrgTxt;

    @ApiModelProperty(value = "项目负责人岗位")
    private String projectManagerPostTxt;

    @ApiModelProperty(value = "备注信息")
    private String remarks;

    @ApiModelProperty(value = "项目状态")
    private ProjectStatus status;

    @ApiModelProperty("流程业务id")
    private String businessId;
}
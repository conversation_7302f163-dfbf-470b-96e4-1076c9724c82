package com.caidaocloud.vms.application.event;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.RoleInitDto;
import com.caidaocloud.vms.application.service.TenantRoleService;
import com.caidaocloud.vms.domain.base.enums.VmsRole;
import com.caidaocloud.vms.infrastructure.feign.IAuthFeignClient;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 *
 * <AUTHOR> Zhou
 * @date 2022/12/16
 */
@Slf4j
@Component
public class TenantInitSubscriber {

	@Autowired
	private TenantRoleService tenantRoleService;

	@RabbitHandler
	@RabbitListener(bindings = @QueueBinding(value = @Queue(value = "vms.tenant.init.queue", durable = "true"), exchange = @Exchange(value = "maintenance.tenant.init.fanout.exchange", type = ExchangeTypes.FANOUT), key = {
			"routingKey.maintenance.tenant.init" }))
	public void process(String message) {
		log.info("Tenant init message={}", message);
		try {
			TenantInitDto dto = FastjsonUtil.toObject(message, TenantInitDto.class);
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(dto.getTenantId());
			userInfo.setEmpId(0L);
			userInfo.setUserId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			tenantRoleService.initCurrentTenantRoles();
			// initVmsRole(dto.getTenantId(), dto.getTenantName(), dto.getCode());
		} catch (Exception ex) {
			log.error("process tenantInitMessage err,{}", ex.getMessage(), ex);
		} finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	private void initVmsRole(String tenantId, String tenantName, String code) {
		try {
			log.info("开始初始化VMS角色，租户ID: {}, 租户名称: {}, 租户编码: {}", tenantId, tenantName, code);

			// 创建角色初始化DTO
			RoleInitDto roleInitDto = new RoleInitDto();
			roleInitDto.setTenantId(tenantId);
			roleInitDto.setTenantName(tenantName);
			roleInitDto.setTenantCode(code);
			roleInitDto.addRole(VmsRole.EMPLOYER);
			roleInitDto.addRole(VmsRole.SUPPLIER);
			roleInitDto.addRole(VmsRole.INTERVIEWER);
			// 调用auth-service接口初始化角色
			Result<String> result = authFeignClient.initTenantRole(roleInitDto);

			if (result.isSuccess()) {
				log.info("VMS角色初始化成功，租户ID: {}, 结果: {}", tenantId, result.getData());
			} else {
				log.error("VMS角色初始化失败，租户ID: {}, 错误信息: {}", tenantId, result.getMsg());
			}
		} catch (Exception ex) {
			log.error("VMS角色初始化异常，租户ID: {}, 错误信息: {}", tenantId, ex.getMessage(), ex);
		}
	}



	@Data
	@ApiModel("租户初始化dto")
	class TenantInitDto {

		@ApiModelProperty("租户id")
		private String tenantId;
		@ApiModelProperty("租户名称")
		private String tenantName;
		@ApiModelProperty("租户code")
		private String code;
	}
}
package com.caidaocloud.vms.infrastructure.feign;

import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * HrPaas服务Feign降级处理
 *
 * @date 2026/01/07
 */
@Component
public class HrPaasFeignFallBack implements IHrPaasFeignClient {

    @Override
    public Result getAddressTree() {
        return Result.fail("获取地址树失败");
    }

    @Override
    public Result getAddressByCodeList(List<String> codeList) {
        return Result.fail("获取地址信息失败");
    }
}

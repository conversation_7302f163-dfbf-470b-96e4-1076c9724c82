package com.caidaocloud.vms.application.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 导入结果VO
 *
 * @date 2026/01/08
 */
@Data
@ApiModel("导入结果")
public class ImportResultVO {

    @ApiModelProperty("进度 0-1")
    private Double progress;

    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("成功数")
    private Integer successCount;

    @ApiModelProperty("失败数")
    private Integer failCount;

    @ApiModelProperty("错误信息列表")
    private List<Map<String, Object>> errors;

    @ApiModelProperty("是否完成")
    private Boolean finished;
}

package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 岗位薪资参考编辑DTO
 *
 * @date 2026/01/08
 */
@Data
@ApiModel("岗位薪资参考编辑")
public class PositionSalaryReferenceUpdateDto {

    @NotBlank(message = "ID不能为空")
    @ApiModelProperty(value = "业务ID", required = true)
    private String bid;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("城市编码")
    private String cityCode;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("行业编码")
    private String industryCode;

    @ApiModelProperty("行业名称")
    private String industryName;

    @ApiModelProperty("岗位名称")
    private String positionName;

    @ApiModelProperty("经验要求（如：3-5年）")
    private String experienceName;

    @ApiModelProperty("25分位薪资（万元/年）")
    private String percentile25;

    @ApiModelProperty("50分位薪资（万元/年）")
    private String percentile50;

    @ApiModelProperty("75分位薪资（万元/年）")
    private String percentile75;

    @ApiModelProperty("90分位薪资（万元/年）")
    private String percentile90;
}

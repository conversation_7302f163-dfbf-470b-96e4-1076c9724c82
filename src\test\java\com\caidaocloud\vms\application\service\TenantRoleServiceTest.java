package com.caidaocloud.vms.application.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.RoleInitDto;
import com.caidaocloud.vms.infrastructure.feign.IAuthFeignClient;
import com.caidaocloud.web.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 租户角色服务测试
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@ExtendWith(MockitoExtension.class)
class TenantRoleServiceTest {

    @Mock
    private IAuthFeignClient authFeignClient;

    @InjectMocks
    private TenantRoleService tenantRoleService;

    private SecurityUserInfo mockUserInfo;

    @BeforeEach
    void setUp() {
        mockUserInfo = new SecurityUserInfo();
        mockUserInfo.setTenantId("test-tenant-123");
        mockUserInfo.setUserId(1L);
        mockUserInfo.setEmpId(1L);
    }

    @Test
    @DisplayName("成功初始化当前租户角色")
    void testInitCurrentTenantRoles_Success() {
        // Given
        Result<String> mockResult = Result.ok("初始化成功");
        when(authFeignClient.initTenantRole(any(RoleInitDto.class))).thenReturn(mockResult);

        // When
        try (MockedStatic<SecurityUserUtil> mockedStatic = mockStatic(SecurityUserUtil.class)) {
            mockedStatic.when(SecurityUserUtil::getSecurityUserInfo).thenReturn(mockUserInfo);
            
            Result<String> result = tenantRoleService.initCurrentTenantRoles();
            
            // Then
            assertTrue(result.isSuccess());
            assertEquals("VMS角色初始化成功", result.getData());
            
            // 验证调用了auth服务
            verify(authFeignClient, times(1)).initTenantRole(any(RoleInitDto.class));
        }
    }

    @Test
    @DisplayName("初始化租户角色失败")
    void testInitCurrentTenantRoles_Failure() {
        // Given
        Result<String> mockResult = Result.fail("初始化失败");
        when(authFeignClient.initTenantRole(any(RoleInitDto.class))).thenReturn(mockResult);

        // When
        try (MockedStatic<SecurityUserUtil> mockedStatic = mockStatic(SecurityUserUtil.class)) {
            mockedStatic.when(SecurityUserUtil::getSecurityUserInfo).thenReturn(mockUserInfo);
            
            Result<String> result = tenantRoleService.initCurrentTenantRoles();
            
            // Then
            assertFalse(result.isSuccess());
            assertTrue(result.getData().contains("VMS角色初始化失败"));
        }
    }

    @Test
    @DisplayName("初始化租户角色异常")
    void testInitCurrentTenantRoles_Exception() {
        // Given
        when(authFeignClient.initTenantRole(any(RoleInitDto.class)))
                .thenThrow(new RuntimeException("网络异常"));

        // When
        try (MockedStatic<SecurityUserUtil> mockedStatic = mockStatic(SecurityUserUtil.class)) {
            mockedStatic.when(SecurityUserUtil::getSecurityUserInfo).thenReturn(mockUserInfo);
            
            Result<String> result = tenantRoleService.initCurrentTenantRoles();
            
            // Then
            assertFalse(result.isSuccess());
            assertTrue(result.getData().contains("VMS角色初始化异常"));
        }
    }
}

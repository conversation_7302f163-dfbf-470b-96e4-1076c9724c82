package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.RuleSetDto;
import com.caidaocloud.vms.application.dto.RuleSetQueryDto;
import com.caidaocloud.vms.application.service.RuleSetService;
import com.caidaocloud.vms.application.vo.RuleSetVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 规则集控制器
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@RestController
@RequestMapping("/api/vms/v1/manager/ruleset")
@Api(tags = "规则设置")
public class RuleSetController {

    @Autowired
    private RuleSetService ruleSetService;

    /**
     * 更新规则集
     * 
     * @param ruleSetDto 规则集信息
     * @return 操作结果
     */
    @PostMapping("/edit")
    @ApiOperation(value = "更新规则集", notes = "更新已有规则集配置")
    public Result edit(
            @ApiParam(value = "规则集信息", required = true) @Valid @RequestBody RuleSetDto ruleSetDto) {
        ruleSetService.edit(ruleSetDto);
        return Result.ok();
    }

    /**
     * 根据ID获取规则集详情
     * 
     * @param id 规则集ID
     * @return 规则集详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取规则集详情", notes = "根据ID获取规则集的详细信息")
    public Result<RuleSetVO> getById(
            @ApiParam(value = "规则集ID", required = true) @RequestParam String id) {
        RuleSetVO vo = ruleSetService.getById(id);
        return Result.ok(vo);
    }

    /**
     * 分页查询规则集
     * 
     * @param queryDto 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询规则集", notes = "根据条件分页查询规则集列表")
    public Result<PageResult<RuleSetVO>> findByPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody RuleSetQueryDto queryDto) {
        PageResult<RuleSetVO> result = ruleSetService.findByPage(queryDto);
        return Result.ok(result);
    }

    /**
     * 获取所有启用的规则集
     * 
     * @return 规则集列表
     */
    @GetMapping("/enabled")
    @ApiOperation(value = "获取启用的规则集", notes = "获取所有启用状态的规则集列表")
    public Result<List<RuleSetVO>> findAllEnabled() {
        List<RuleSetVO> result = ruleSetService.findAllEnabled();
        return Result.ok(result);
    }

    /**
     * 删除规则集
     * 
     * @param id 规则集ID
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除规则集", notes = "根据ID删除规则集")
    public Result delete(
            @ApiParam(value = "规则集ID", required = true) @RequestParam String id) {
        ruleSetService.delete(id);
        return Result.ok();
    }

    /**
     * 启用/禁用规则集
     * 
     * @param id 规则集ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    @PostMapping("/status")
    @ApiOperation(value = "更新规则集状态", notes = "启用或禁用规则集")
    public Result updateStatus(
            @ApiParam(value = "规则集ID", required = true) @RequestParam String id,
            @ApiParam(value = "是否启用", required = true) @RequestParam Boolean enabled) {
        ruleSetService.updateStatus(id, enabled);
        return Result.ok();
    }
}

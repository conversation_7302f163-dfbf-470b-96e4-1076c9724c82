package com.caidaocloud.vms.application.service.ats;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataBasicInfoDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
//import com.ruipin.metadata.domain.dto.EntityModuleDefDto;
//import com.ruipin.metadata.domain.dto.EntityPropertyDefDto;
//import com.ruipin.metadata.domain.vo.EntityDefVo;
//import com.ruipin.metadata.domain.vo.EntityModuleDefVo;
//import com.ruipin.metadata.domain.vo.EntityPropertyDefVo;
//import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2026/1/12
 */
@Service
public class AtsModuleService {

//	public List<MetadataDto> convert2PaasModule(EntityDefVo atsDef) {
//		List<MetadataDto> list = new ArrayList<>();
//		for (EntityModuleDefVo moduleDefVo : atsDef.getExtModule()) {
//			MetadataDto metadataDto = convert2PaasModule(moduleDefVo);
//			list.add(metadataDto);
//		}
//		return list;
//	}
//
//	public MetadataDto convert2PaasModule(EntityModuleDefVo moduleDefVo) {
//		MetadataBasicInfoDto dto = new MetadataBasicInfoDto();
//		// todo 确认ats的identifier和name字段
//		dto.setIdentifier(moduleDefVo.getEntityId());
//		dto.setOwner("ats");
//		dto.setName(moduleDefVo.getModuleName());
//		dto.setI18nName(Maps.map("default", dto.getName()));
//
//		List<MetadataPropertyDto> propertyDtoList = Sequences.sequence(moduleDefVo.getPropertyList())
//				.map(this::convert).toList();
//
//		MetadataDto metadataDto = new MetadataDto();
//		metadataDto.setBasicInfo(dto);
//		metadataDto.setProperties(propertyDtoList);
//		return metadataDto;
//	}
//
//	public MetadataDto convert2PaasModule(EntityModuleDefDto moduleDefVo) {
//		MetadataBasicInfoDto dto = new MetadataBasicInfoDto();
//		// todo 确认ats的identifier和name字段
//		dto.setIdentifier(moduleDefVo.getEntityId());
//		dto.setOwner("ats");
//		dto.setName(moduleDefVo.getModuleName());
//		dto.setI18nName(Maps.map("default", dto.getName()));
//
//		List<MetadataPropertyDto> propertyDtoList = new ArrayList<>();
//
//		MetadataDto metadataDto = new MetadataDto();
//		metadataDto.setBasicInfo(dto);
//		metadataDto.setProperties(propertyDtoList);
//		return metadataDto;
//	}
//
//	public MetadataPropertyDto convert(EntityPropertyDefVo def) {
//		return MetadataPropertyBuilder.builder(def.getInputType())
//				.name(def.getPropertyName())
//				.property(def.getPropertyName())
//				.enumSelection(def.getSelections())
//				.build();
//	}
//
//
//	public MetadataPropertyDto convert(EntityPropertyDefDto def) {
//		return MetadataPropertyBuilder.builder(def.getInputType())
//				.name(def.getPropertyName())
//				.property(def.getPropertyName())
//				.enumSelection(def.getSelection())
//				.build();
//	}
}

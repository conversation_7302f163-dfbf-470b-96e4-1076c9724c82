package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.vms.domain.base.enums.EmployeeManagementMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则集查询DTO
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "规则集查询条件")
public class RuleSetQueryDto extends BasePage {
    
    @ApiModelProperty(value = "规则集名称（模糊查询）")
    private String name;
    
    @ApiModelProperty(value = "员工管理模式")
    private EmployeeManagementMode mode;
    
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
}

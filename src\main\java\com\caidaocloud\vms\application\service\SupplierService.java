package com.caidaocloud.vms.application.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.annotation.Resource;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.SupplierContactDto;
import com.caidaocloud.vms.application.dto.SupplierContractDto;
import com.caidaocloud.vms.application.dto.SupplierDto;
import com.caidaocloud.vms.application.dto.SupplierQueryDTO;
import com.caidaocloud.vms.application.dto.SupplierTaxInfoDto;
import com.caidaocloud.vms.application.vo.SupplierContactVO;
import com.caidaocloud.vms.application.vo.SupplierContractVO;
import com.caidaocloud.vms.application.vo.SupplierDetailVO;
import com.caidaocloud.vms.application.vo.SupplierPageVO;
import com.caidaocloud.vms.application.vo.SupplierSelectVO;
import com.caidaocloud.vms.application.vo.SupplierTaxInfoVO;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContract;
import com.caidaocloud.vms.domain.supplier.entity.SupplierTaxInfo;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import com.caidaocloud.vms.domain.supplier.enums.SupplierStatus;
import com.caidaocloud.vms.domain.supplier.repository.SupplierContactRepository;
import com.caidaocloud.vms.domain.supplier.repository.SupplierContractRepository;
import com.caidaocloud.vms.domain.supplier.repository.SupplierRepository;
import com.googlecode.totallylazy.Sequences;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/5/16
 */
@Service
public class SupplierService {

	@Resource
	private SupplierRepository supplierRepository;
	@Resource
	private SupplierContactRepository supplierContactRepository;
	@Resource
	private SupplierContractRepository supplierContractRepository;

	@PaasTransactional
	public void saveSupplier(SupplierDto supplierDto) {
		// 创建供应商基本信息
		Supplier supplier = new Supplier(supplierDto.getSupplierCode(), supplierDto.getSupplierName());

		// 保存供应商
		supplierRepository.saveOrUpdate(supplier);
	}

	@PaasTransactional
	public void editSupplier(SupplierDto supplierDto) {
		// 获取供应商
		Supplier supplier = supplierRepository.getById(supplierDto.getBid());
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierDto.getBid());
		}

		// 更新基本信息
		supplier.updateBasicInfo(supplierDto);

		// 保存供应商
		supplierRepository.saveOrUpdate(supplier);
	}

	/**
	 * 分页查询供应商列表
	 * 
	 * @param queryDTO 查询条件，包含分页信息和供应商名称（用于模糊查询）
	 * @return 供应商列表，包含id、编码、名称、人员规模、营收规模、擅长行业
	 */
	public PageResult<SupplierPageVO> supplierPage(SupplierQueryDTO queryDTO) {

		// 执行分页查询
		PageResult<Supplier> supplierPage = supplierRepository.findByPage(queryDTO.getSupplierName(), queryDTO);

		List<SupplierPageVO> voList = convert2Vo(supplierPage.getItems());

		return new PageResult<>(voList, supplierPage.getPageNo(), supplierPage.getPageSize(), supplierPage.getTotal());
	}

	private List<SupplierPageVO> convert2Vo(List<Supplier> list) {
		// 转换为VO列表
		List<SupplierPageVO> voList = list.stream()
				.map(supplier -> {
					SupplierPageVO vo = ObjectConverter.convert(supplier, SupplierPageVO.class);
					List<DictSimple> dictList = Sequences.sequence(supplier.getIndustry())
							.map(DictSimple::doDictSimple).toList();
					vo.setIndustry(dictList);
					// 为vo的year赋值，根据supplier的endDate和startDate毫秒级时间戳计算年月差值，保留1位小数
					if (supplier.getStartDate() != null && supplier.getEndDate() != null) {
						// 计算时间差（毫秒）
						long timeDiff = supplier.getEndDate() - supplier.getStartDate();

						// 转换为年（包含小数部分）
						// 1年 = 365.25天 = 365.25 * 24 * 60 * 60 * 1000毫秒
						double yearsDiff = timeDiff / (365.25 * 24 * 60 * 60 * 1000);

						// 保留1位小数
						BigDecimal yearDecimal = new BigDecimal(yearsDiff).setScale(1, RoundingMode.HALF_UP);

						vo.setYear(yearDecimal);
					}
					vo.setStatus(SupplierStatus.fromCode(Integer.parseInt(supplier.getStatus().getValue())));
					return vo;
				})
				.collect(Collectors.toList());
		return voList;
	}

	/**
	 * 根据ID加载供应商基础信息
	 * 
	 * @param supplierId 供应商ID
	 * @return 供应商详细信息
	 */
	public SupplierDetailVO loadSupplier(String supplierId) {
		// 根据bid查询供应商基础信息
		Supplier supplier = supplierRepository.getById(supplierId);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierId);
		}

		return ObjectConverter.convert(supplier, SupplierDetailVO.class);
	}

	/**
	 * 根据ID加载供应商基础信息并赋值txt文本
	 *
	 * @param supplierId 供应商ID
	 * @return 供应商详细信息
	 */
	public SupplierDetailVO loadSupplierTxt(String supplierId) {
		SupplierDetailVO vo = loadSupplier(supplierId);

		// 这里可以根据需要为供应商相关字段赋值txt文本
		// 例如：为供应商所属行业、地区等字段赋值

		return vo;
	}

	/**
	 * 将供应商状态变更为终止
	 * 
	 * @param supplierId 供应商ID
	 */
	@PaasTransactional
	public void terminated(String supplierId) {
		// 将供应商变为终止状态
		Supplier supplier = supplierRepository.getById(supplierId);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierId);
		}

		// 设置状态为终止
		supplier.terminated();

		// 保存供应商
		supplierRepository.saveOrUpdate(supplier);
	}

	/**
	 * 根据ID加载供应商税务信息
	 * 
	 * @param supplierId 供应商ID
	 * @return 供应商税务信息
	 */
	public SupplierTaxInfoVO loadSupplierTaxInfo(String supplierId) {
		// 根据bid查询供应商税务信息
		Supplier supplier = supplierRepository.getById(supplierId);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierId);
		}

		SupplierTaxInfo taxInfo = supplier.getTaxInfo();
		if (taxInfo == null) {
			return new SupplierTaxInfoVO();
		}

		return ObjectConverter.convert(taxInfo, SupplierTaxInfoVO.class);
	}

	@PaasTransactional
	public void editTaxInfo(SupplierTaxInfoDto taxInfoDto) {
		// 获取供应商
		Supplier supplier = supplierRepository.getById(taxInfoDto.getSupplierId());
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + taxInfoDto.getSupplierId());
		}

		// 获取或创建税务信息
		SupplierTaxInfo taxInfo = supplier.getTaxInfo();
		if (taxInfo == null) {
			taxInfo = new SupplierTaxInfo();
			taxInfo.setSupplierId(supplier.getBid());
			supplier.setTaxInfo(taxInfo);
		}

		supplier.updateTaxInfo(taxInfoDto);

		supplierRepository.saveOrUpdateTaxInfo(supplier.getTaxInfo());
	}

	@PaasTransactional
	public void saveContact(SupplierContactDto contactDto) {
		// 获取供应商
		Supplier supplier = supplierRepository.getById(contactDto.getSupplierId());
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + contactDto.getSupplierId());
		}

		// 初始化联系人
		SupplierContact contact = new SupplierContact(
				supplier.getBid(),
				contactDto.getContact(),
				contactDto.getPosition(),
				contactDto.getEmail(),
				contactDto.getPhone());
		contact.update(contactDto);
		contact.checkValidate();

		// 处理主要联系人逻辑
		if (contactDto.getIsPrimary()) {
			// 如果设置为主要联系人，需要先取消当前的主要联系人
			Optional<SupplierContact> currentPrimaryOpt = supplierContactRepository
					.findPrimaryContactBySupplier(supplier.getBid());
			if (currentPrimaryOpt.isPresent()) {
				SupplierContact currentPrimary = currentPrimaryOpt.get();
				currentPrimary.unsetPrimary();
				supplierContactRepository.saveOrUpdate(currentPrimary);
			}
		}

		// 保存联系人
		String contactId = supplierContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 获取供应商所有联系人信息
	 * 
	 * @param supplierId 供应商ID
	 * @return 联系人列表
	 */
	public List<SupplierContactVO> supplierContactList(String supplierId) {
		// 获取供应商所有联系人信息
		Supplier supplier = supplierRepository.getById(supplierId);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierId);
		}
		List<SupplierContact> contactList = supplierContactRepository.loadContactListBySupplier(supplierId);

		// 转换为VO列表
		return contactList.stream()
				.map(contact -> {
					SupplierContactVO vo = ObjectConverter.convert(contact, SupplierContactVO.class);
					vo.setStatus(ActiveStatus.fromCode(Integer.parseInt(contact.getStatus().getValue())));
					return vo;
				})
				.collect(Collectors.toList());
	}

	@PaasTransactional
	public void editContact(SupplierContactDto contactDto) {
		// 查找联系人
		Optional<SupplierContact> contact = supplierContactRepository.getContact(contactDto.getBid());

		if (!contact.isPresent()) {
			throw new ServerException("Contact not found: " + contactDto.getBid());
		}

		SupplierContact currentContact = contact.get();
		boolean wasNotPrimary = !currentContact.getIsPrimary();

		// 更新联系人信息
		currentContact.update(contactDto);
		currentContact.checkValidate();

		// 处理主要联系人逻辑
		if (contactDto.getIsPrimary() && wasNotPrimary) {
			// 如果从非主要联系人变为主要联系人，需要先取消当前的主要联系人
			Optional<SupplierContact> currentPrimaryOpt = supplierContactRepository
					.findPrimaryContactBySupplier(currentContact.getSupplierId());
			if (currentPrimaryOpt.isPresent() && !currentPrimaryOpt.get().getBid().equals(contactDto.getBid())) {
				SupplierContact currentPrimary = currentPrimaryOpt.get();
				currentPrimary.unsetPrimary();
				supplierContactRepository.saveOrUpdate(currentPrimary);
			}
		}

		// 保存联系人
		supplierContactRepository.saveOrUpdate(currentContact);
	}

	@PaasTransactional
	public void saveContract(SupplierContractDto supplierContractDto) {
		// 获取供应商
		Supplier supplier = supplierRepository.getById(supplierContractDto.getSupplierId());
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierContractDto.getSupplierId());
		}

		// 初始化合同
		SupplierContract contract = new SupplierContract(
				supplier.getBid(),
				supplierContractDto.getContractName(),
				supplierContractDto.getEffectiveDate(),
				supplierContractDto.getExpiryDate());
		contract.update(supplierContractDto);
		supplier.updateContract(contract);

		// 保存合同
		supplierRepository.saveOrUpdate(supplier);
		String contactId = supplierContractRepository.saveOrUpdate(contract);
	}

	@PaasTransactional
	public void editContract(SupplierContractDto supplierContractDto) {
		// 获取供应商
		Supplier supplier = supplierRepository.getById(supplierContractDto.getSupplierId());
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierContractDto.getSupplierId());
		}

		// 查找合同
		Optional<SupplierContract> contract = supplierContractRepository.getContract(supplierContractDto.getBid());

		if (!contract.isPresent()) {
			throw new ServerException("Contract not found: " + supplierContractDto.getBid());
		}

		// 更新合同信息
		contract.get().update(supplierContractDto);
		supplier.updateContract(contract.get());

		// 保存联系人
		supplierRepository.saveOrUpdate(supplier);
		supplierContractRepository.saveOrUpdate(contract.get());
	}

	/**
	 * 获取供应商所有合同信息
	 * 
	 * @param supplierId 供应商ID
	 * @return 合同列表
	 */
	public List<SupplierContractVO> supplierContractList(String supplierId) {
		// 获取供应商所有合同信息
		Supplier supplier = supplierRepository.getById(supplierId);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierId);
		}
		List<SupplierContract> contractList = supplierContractRepository.listContract(supplierId);

		// 转换为VO列表
		return contractList.stream()
				.map(contract -> ObjectConverter.convert(contract, SupplierContractVO.class))
				.collect(Collectors.toList());
	}

	@PaasTransactional
	public void delete(String bid) {
		// 将供应商变为终止状态
		Supplier supplier = supplierRepository.getById(bid);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + bid);
		}

		// 保存供应商
		supplierRepository.delete(supplier);
	}

	/**
	 * 启用供应商联系人
	 * 
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void activateContact(String contactId) {
		// 查找联系人
		Optional<SupplierContact> contactOpt = supplierContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 更新联系人状态为启用
		SupplierContact contact = contactOpt.get();
		contact.active();

		// 保存联系人
		supplierContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 停用供应商联系人
	 * 
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void deactivateContact(String contactId) {
		// 查找联系人
		Optional<SupplierContact> contactOpt = supplierContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 更新联系人状态为停用
		SupplierContact contact = contactOpt.get();
		contact.inactive();

		// 保存联系人
		supplierContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 删除供应商联系人
	 * 
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void deleteContact(String contactId) {
		// 查找联系人
		Optional<SupplierContact> contactOpt = supplierContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 删除联系人
		supplierContactRepository.deleteContact(contactId);
	}

	/**
	 * 删除供应商合同
	 * 
	 * @param contractId 合同ID
	 */
	@PaasTransactional
	public void deleteContract(String contractId) {
		// 查找合同
		Optional<SupplierContract> contractOpt = supplierContractRepository.getContract(contractId);

		if (!contractOpt.isPresent()) {
			throw new ServerException("Contract not found: " + contractId);
		}

		// 获取供应商
		Supplier supplier = supplierRepository.getById(contractOpt.get().getSupplierId());
		if (supplier == null) {
			throw new ServerException("Supplier not found for contract: " + contractId);
		}

		// 删除合同
		supplierContractRepository.deleteContract(contractId);

		// todo 更新供应商的合同日期范围
		// 这里可能需要重新计算供应商的合同日期范围，但由于没有看到相关代码，暂时省略
		// 如果需要，可以添加一个方法来重新计算供应商的合同日期范围
	}

	public List<SupplierPageVO> listSupplier(List<String> idList) {
		List<Supplier> list = supplierRepository.list(idList);

		return convert2Vo(list);
	}

	public Map<String, List<SupplierContact>> loadSupplierLatestContact(List<String> supplierIds) {
		if (supplierIds.isEmpty()) {
			return new HashMap<>();
		}
		List<SupplierContact> list = supplierContactRepository.loadContactListBySuppliers(supplierIds,
				ActiveStatus.ACTIVE);
		return Sequences.sequence(list).toMap(SupplierContact::getSupplierId);
	}

	public List<SupplierContact> loadContact(List<String> contactIds) {
		if (contactIds.isEmpty()) {
			return new ArrayList<>();
		}
		List<SupplierContact> list = supplierContactRepository.loadContactList(contactIds);
		return list;
	}

	/**
	 * 根据用户ID查找关联的供应商ID
	 * 
	 * @param userId 用户ID
	 * @return 供应商ID，如果未找到则返回null
	 */
	public String findSupplierIdByUserId(String userId) {
		List<SupplierContact> contacts = supplierContactRepository.findByCreateBy(userId);
		if (!contacts.isEmpty()) {
			// 返回第一个找到的供应商ID
			return contacts.get(0).getSupplierId();
		}
		return null;
	}

	public List<SupplierSelectVO> supplierList() {
		List<Supplier> list = supplierRepository.list();
		return Sequences.sequence(list).map(l -> ObjectConverter.convert(l, SupplierSelectVO.class)).toList();
	}

	/**
	 * 所有启用中的联系人
	 * 
	 * @param supplierId
	 * @return
	 */
	public List<SupplierContactVO> supplierContactSelectList(String supplierId) {
		List<SupplierContact> contactList = supplierContactRepository.loadContactListBySupplier(supplierId);
		return Sequences.sequence(contactList)
				.filter(c -> String.valueOf(ActiveStatus.ACTIVE.getCode()) == c.getStatus().getValue())
				.map(contract -> ObjectConverter.convert(contract, SupplierContactVO.class))
				.toList();
	}

	/**
	 *
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void enableContactPrimary(String contactId) {
		// 查找联系人
		Optional<SupplierContact> contactOpt = supplierContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		SupplierContact contact = contactOpt.get();
		String supplierId = contact.getSupplierId();

		// 查找当前的主要联系人
		Optional<SupplierContact> currentPrimaryOpt = supplierContactRepository
				.findPrimaryContactBySupplier(supplierId);

		// 如果存在当前主要联系人且不是同一个人，则取消其主要联系人状态
		if (currentPrimaryOpt.isPresent() && !currentPrimaryOpt.get().getBid().equals(contactId)) {
			SupplierContact currentPrimary = currentPrimaryOpt.get();
			currentPrimary.unsetPrimary();
			supplierContactRepository.saveOrUpdate(currentPrimary);
		}

		// 设置新的主要联系人
		contact.setPrimary();
		supplierContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 取消联系人的主要联系人状态
	 *
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void disableContactPrimary(String contactId) {
		// 查找联系人
		Optional<SupplierContact> contactOpt = supplierContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 取消主要联系人状态
		SupplierContact contact = contactOpt.get();
		contact.unsetPrimary();

		// 保存联系人
		supplierContactRepository.saveOrUpdate(contact);
	}

	public void active(String supplierId) {
		// 将供应商变为终止状态
		Supplier supplier = supplierRepository.getById(supplierId);
		if (supplier == null) {
			throw new ServerException("Supplier not found: " + supplierId);
		}

		// 设置状态为终止
		supplier.active();

		// 保存供应商
		supplierRepository.saveOrUpdate(supplier);
	}
}

package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.vms.application.service.TenantRoleService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租户角色管理控制器
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@RestController
@RequestMapping("/api/vms/v1/manager/tenant/role")
@Api(tags = "租户角色管理", description = "租户角色初始化相关接口")
public class TenantRoleController {

    @Autowired
    private TenantRoleService tenantRoleService;

    /**
     * 手动初始化当前租户的VMS角色
     * 
     * @return 初始化结果
     */
    @PostMapping("/init")
    @ApiOperation(value = "初始化当前租户VMS角色", notes = "手动为当前租户初始化用工方、供应商、面试官三个VMS角色")
    public Result<String> initCurrentTenantRoles() {
        return tenantRoleService.initCurrentTenantRoles();
    }
}

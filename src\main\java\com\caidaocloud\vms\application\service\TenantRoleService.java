package com.caidaocloud.vms.application.service;

import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.RoleInitDto;
import com.caidaocloud.vms.domain.base.enums.VmsRole;
import com.caidaocloud.vms.infrastructure.feign.IAuthFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 租户角色服务
 * 
 * <AUTHOR>
 * @date 2025/1/7
 */
@Slf4j
@Service
public class TenantRoleService {

    @Autowired
    private IAuthFeignClient authFeignClient;

    /**
     * 为当前租户初始化VMS角色
     * 
     * @return 初始化结果
     */
    public Result<String> initCurrentTenantRoles() {
        try {
            String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
            log.info("开始为当前租户初始化VMS角色，租户ID: {}", tenantId);
            
            // 创建角色初始化DTO
            RoleInitDto roleInitDto = new RoleInitDto();
            roleInitDto.setTenantId(tenantId);

            // 添加VMS角色
            roleInitDto.addRole(VmsRole.EMPLOYER);
            roleInitDto.addRole(VmsRole.SUPPLIER);
            roleInitDto.addRole(VmsRole.INTERVIEWER);
            
            // 调用auth-service接口初始化角色
            Result<String> result = authFeignClient.initTenantRole(roleInitDto);
            
            if (result.isSuccess()) {
                log.info("当前租户VMS角色初始化成功，租户ID: {}, 结果: {}", tenantId, result.getData());
                return Result.ok("VMS角色初始化成功");
            } else {
                log.error("当前租户VMS角色初始化失败，租户ID: {}, 错误信息: {}", tenantId, result.getMsg());
                return Result.fail("VMS角色初始化失败: " + result.getMsg());
            }
        } catch (Exception ex) {
            log.error("当前租户VMS角色初始化异常，错误信息: {}", ex.getMessage(), ex);
            return Result.fail("VMS角色初始化异常: " + ex.getMessage());
        }
    }
}

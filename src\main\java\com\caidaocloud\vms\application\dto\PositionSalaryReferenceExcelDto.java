package com.caidaocloud.vms.application.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 岗位薪资参考Excel导入导出DTO
 *
 * @date 2026/01/08
 */
@Data
public class PositionSalaryReferenceExcelDto {

    @ExcelProperty("年份")
    @ColumnWidth(15)
    private Integer year;

    @ExcelProperty("城市")
    @ColumnWidth(15)
    private String cityName;

    @ExcelProperty("行业")
    @ColumnWidth(25)
    private String industryName;

    @ExcelProperty("岗位")
    @ColumnWidth(25)
    private String positionName;

    @ExcelProperty("经验要求")
    @ColumnWidth(25)
    private String experienceName;

    @ExcelProperty("25分位(万/年)")
    @ColumnWidth(15)
    private String percentile25;

    @ExcelProperty("50分位(万/年)")
    @ColumnWidth(15)
    private String percentile50;

    @ExcelProperty("75分位(万/年)")
    @ColumnWidth(15)
    private String percentile75;

    @ExcelProperty("90分位(万/年)")
    @ColumnWidth(15)
    private String percentile90;
}

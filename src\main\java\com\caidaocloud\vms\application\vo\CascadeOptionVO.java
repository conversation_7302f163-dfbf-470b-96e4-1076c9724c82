package com.caidaocloud.vms.application.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 级联选项VO
 *
 * @date 2026/01/07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("级联选项")
public class CascadeOptionVO {

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("子选项")
    private List<CascadeOptionVO> children;

    public CascadeOptionVO(String code, String name) {
        this.code = code;
        this.name = name;
    }
}

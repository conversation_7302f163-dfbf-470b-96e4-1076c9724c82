package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.RuleSetQueryDto;
import com.caidaocloud.vms.domain.base.entity.RuleSet;
import com.caidaocloud.vms.domain.base.repository.RuleSetRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 规则集仓储实现
 * 
 * <AUTHOR>
 * @date 2025/12/31
 */
@Repository
public class RuleSetRepositoryImpl implements RuleSetRepository {

    @Override
    public void saveOrUpdate(RuleSet ruleSet) {
        if (StringUtils.isEmpty(ruleSet.getBid())) {
            DataInsert.identifier(RuleSet.identifier).insert(ruleSet);
        } else {
            DataUpdate.identifier(RuleSet.identifier).update(ruleSet);
        }
    }

    @Override
    public Optional<RuleSet> getById(String id) {
        if (StringUtils.isEmpty(id)) {
            return Optional.empty();
        }
        RuleSet ruleSet = DataQuery.identifier(RuleSet.identifier)
                .oneOrNull(id, RuleSet.class);
        return Optional.ofNullable(ruleSet);
    }

    @Override
    public Optional<RuleSet> getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return Optional.empty();
        }
        List<RuleSet> ruleSets = DataQuery.identifier(RuleSet.identifier)
                .filter(DataFilter.eq("name", name)
                        .andNe("deleted", Boolean.TRUE.toString()), RuleSet.class)
                .getItems();
        return ruleSets.isEmpty() ? Optional.empty() : Optional.of(ruleSets.get(0));
    }

    @Override
    public PageResult<RuleSet> findByPage(RuleSetQueryDto queryDto) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());
        
        if (StringUtils.isNotEmpty(queryDto.getName())) {
            filter = filter.andRegex("name", queryDto.getName());
        }
        
        if (queryDto.getMode() != null) {
            filter = filter.andEq("mode", String.valueOf(queryDto.getMode().getCode()));
        }
        
        if (queryDto.getEnabled() != null) {
            filter = filter.andEq("enabled", queryDto.getEnabled().toString());
        }
        
        return DataQuery.identifier(RuleSet.identifier)
                .limit(queryDto.getPageSize(), queryDto.getPageNum())
                .filter(filter, RuleSet.class)
                .getPageResult();
    }

    @Override
    public List<RuleSet> findAllEnabled() {
        return DataQuery.identifier(RuleSet.identifier)
                .filter(DataFilter.eq("enabled", Boolean.TRUE.toString())
                        .andNe("deleted", Boolean.TRUE.toString()), RuleSet.class)
                .getItems();
    }

    @Override
    public void deleteById(String id) {
        DataDelete.identifier(RuleSet.identifier).softDelete(id);
    }

    @Override
    public boolean existsByName(String name, String excludeId) {
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        
        DataFilter filter = DataFilter.eq("name", name)
                .andNe("deleted", Boolean.TRUE.toString());
        
        if (StringUtils.isNotEmpty(excludeId)) {
            filter = filter.andNe("bid", excludeId);
        }
        
        List<RuleSet> ruleSets = DataQuery.identifier(RuleSet.identifier)
                .filter(filter, RuleSet.class)
                .getItems();
        
        return !ruleSets.isEmpty();
    }
}

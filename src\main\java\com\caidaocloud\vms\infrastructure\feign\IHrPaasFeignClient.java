package com.caidaocloud.vms.infrastructure.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(
        contextId = "hrPaasFeignClient",
        value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        fallback = HrPaasFeignFallBack.class,
        configuration = FeignConfiguration.class
)
public interface IHrPaasFeignClient {

    /**
     * 获取地址树（省市区）
     */
    @GetMapping("/api/hrpaas/v1/address/tree")
    Result getAddressTree();

    /**
     * 根据地址code列表批量查询地址信息
     */
    @PostMapping("/api/hrpaas/v1/address/codeList")
    Result getAddressByCodeList(@RequestBody List<String> codeList);
}

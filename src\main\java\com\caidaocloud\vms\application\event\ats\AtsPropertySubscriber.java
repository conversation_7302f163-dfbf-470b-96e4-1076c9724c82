package com.caidaocloud.vms.application.event.ats;

import java.util.List;

import com.caidaocloud.masterdata.attendance.application.dto.WaLeaveTypeDefDto;
import com.caidaocloud.masterdata.attendance.application.service.LeaveTypeDefService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.service.ats.AtsModuleService;
import com.ruipin.metadata.domain.EntityPropertyDefChangeMessage;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2023/8/8
 */
@Slf4j
@Component
public class AtsPropertySubscriber {
	@Autowired
	private AtsModuleService atsModuleService;

	@RabbitListener(
			bindings = @QueueBinding(
					value = @Queue(value = "vms.ats.property.change.queue", durable = "true"),
					exchange = @Exchange(value = "caidao.hrpaas"),
					key = {"caidao.ats.property.change"}
			)
	)
	@RabbitHandler
	public void process(String msg) {
		log.info("Subscribe leave type msg,{}", msg);
		FastjsonUtil.toObject(msg, EntityPropertyDefChangeMessage.class);
		List<WaLeaveTypeDefDto> typeDefDtoList = FastjsonUtil.toList(msg, WaLeaveTypeDefDto.class);
		try {
			if (typeDefDtoList.isEmpty()) {
				return;
			}
			String tenantId = typeDefDtoList.get(0).getBelongOrgid();
			SecurityUserInfo info = new SecurityUserInfo();
			info.setTenantId(tenantId);
			info.setUserId(0L);
			info.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(info);
			leaveTypeDefService.saveLeaveType(typeDefDtoList);
		}catch (Exception e){
			log.error("Leave type sync error", e);
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
			log.info("Leave type sync end");

		}

	}
}
